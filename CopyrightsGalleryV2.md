# Product Requirements Document (PRD): Copyright Gallery v2

## 1) Overview

The Copyright Gallery displays images and lets reviewers perform actions on individual files and in bulk. This release restructures the gallery around **plaintiffs**, adds a new **CN Websites** subsection, introduces metadata editing (e.g., **type**), improves the **Info Panel**, and implements utilities like **Split** and a **Reg Number Allocation Tool**.

## 2) Goals & Non‑Goals

**Goals**

* Group gallery content by **plaintiff** with two subsections per plaintiff: (a) **copyrights_files** and (b) **cn_websites_files**.
* Sort pictures by **reg_no** across both subsections  (but reg_no starting with <PERSON> go last).
* Improve the **side panel**: show more metadata; enable editing for **production flag**, **method**, and **type**; remove **certificate status**; merge **certificate** into **Preview**.
* Add **Split** tool for mosaics and a Watermark removal tool (placeholder for only for now).
* Enable bulk actions (copyrights_files: set production true/false; set a type. cn_websites_files: move to copyrights_files).
* Provide a one‑click **Move** from cn_websites_files to copyrights_files from the side panel (and via bulk and via drag and drop).
* Add **Reg number allocation tool**.

**Non‑Goals**

* Implementing watermark removal algorithms and split tool (UI placeholder only).
* Changing existing selection behavior, image overlay, or side‑panel layout patterns.

## 3) Information Architecture (IA)

**Gallery Page**

* Top: global filters (keep current ones, but remove Certificate Status), and bulk action toolbar (contextual to current subsection selection).
* Body:
  * **Plaintiff Section** (collapsible). Heading = Plaintiff name + Plaintiff ids + counts.
    * **Subsection A — Copyrights Files** (cards grid)
    * **Subsection B — CN Websites Files** (cards grid)

Within each subsection, cards are **sorted by reg_no** ascending, but put the reg_no starting with MD last. The reg_no are already in stadard formar: 2 or 3 letters followed by 10 or 9 numbers, or MD followed by 8 numbers

## 4) Card & Interactions (unchanged unless noted)

* **Checkbox for selection**: unchanged. Used for bulk ops.
* **Click on image**: opens **large image overlay** (unchanged).
* **Click on card body (not image)**: opens **side panel** (unchanged interaction pattern).
* **Background color rule (NEW)**: If `<span>production = true</span>` → Card background **Blue** (#1976d2).
* It is possible to drag anddrop a cn_websites_files card into copyrights_files (of the same plaintiff). When this is done migration is triggered (see section 9.1)

## 5) Side Panel (per file)

Tabs remain if already used for structure, but **Certificate tab is removed**. Key sections:

### 5.1 Info Panel (for copyrights_files only)

* **Show**: registration number (`<span>reg_no</span>`), filename, dimensions, created_at/updated_at, source, etc.
* **Production flag**: toggle on/off (already in FE; ensure BE endpoint supports update).
* **Method**: dropdown. Must display currently set value. Requires BE read/write endpoints and a catalog list.
* **Type**: dropdown (NEW). Requires BE read/write and catalog list.
* **Certificate status**: **remove** from UI and ignore in responses.

### 5.2 Info Panel (for cn_websites_files only)

* **Show** all fields from `<span>cn_websites_files</span>` and joined `<span>cn_websites</span>`, including at minimum: `<span>reg_no</span>`, `<span>docket_in_title</span>`, `<span>docket_formatted</span>`, `<span>case_id</span>`, `<span>url</span>`, `<span>type</span>`, `<span>website</span>`, `<span>site_domain</span>`, `<span>post_date</span>` (TBD), `<span>sha1</span>` (if present), etc. Display in a tidy key‑value layout with copy‑to‑clipboard.
* **Move to copyrights_files** button (primary action). On click: (see §9.1)

### 5.3 Tools (for copyrights_files only)

* **Crop**: existing tool, unchanged.
* **Split (NEW)**: launch an editor to define multiple regions; saving creates **new child files** (see §10.2) within the same plaintiff context. Prompt to optionally inherit `<span>reg_no</span>`, `<span>type</span>`, and `<span>production</span>` from the parent or set individually.
* **Remove watermark (NEW placeholder)**: disabled state with tooltip “Coming soon”.

### 5.4 Preview (updated) (for copyrights_files only)

* **High‑quality image preview** on top (existing).
* **Certificate preview below the image** (migrated from the old Certificate tab). If certificate not available, show empty state with link/CTA to attach.

### 5.5 Reg Number Allocation Tool (NEW) (for copyrights_files only)

* In side panel, a section "Assign RegNo" with a search‑select listing **all registration numbers for this plaintiff** with their **title** (from `<span>copyrights.title</span>`).
* Selecting an entry updates the file’s `<span>reg_no</span>`.
* Show current assignment and last updated by/on.

## 6) Bulk Actions

### 6.1 copyright_files

* Set `<span>production = true</span>` for selected.
* Set `<span>production = false</span>` for selected.
* Set **Type** (single value applied to all selected): need to ask the user for a value, or select form a dropdown of existing values

### 6.2 cn_websites_files

* **Move to copyrights_files** for selected items. See 9.1

Bulk toolbar appears when ≥1 item is selected in the **current subsection**. Bulk ops never mix across subsections. If the selection include files from both subsection, the not bulk option available

## 7) Data Model & Schema: Existing tables

-- Table Definition
CREATE TABLE "public"."copyrights" (
    "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
    "tro" bool,
    "registration_number" text,
    "registration_date" date,
    "type_of_work" text,
    "title" text,
    "date_of_creation" int4,
    "date_of_publication" date,
    "copyright_claimant" text,
    "authorship_on_application" text,
    "rights_and_permissions" text,
    "description" text,
    "nation_of_first_publication" text,
    "names" text,
    "plaintiff_id" int4,
    "create_time" timestamptz NOT NULL DEFAULT now(),
    "update_time" timestamptz NOT NULL DEFAULT now(),
    "deleted" bool NOT NULL DEFAULT false,
    "certificate_status" varchar(20),
    PRIMARY KEY ("id")
);

-- Table Definition
CREATE TABLE "public"."copyrights_files" (
    "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
    "filename" text,
    "registration_number" text,
    "method" text,
    "production" bool,
    "type" varchar(50),
    "create_time" timestamptz NOT NULL DEFAULT now(),
    "update_time" timestamptz NOT NULL DEFAULT now(),
    PRIMARY KEY ("id")
);

-- Table Definition
CREATE TABLE "public"."cn_websites" (
    "id" int4 NOT NULL DEFAULT nextval('cn_websites_id_seq'::regclass),
    "posting_date" date,
    "docket_in_title" text,
    "docket_formated" text,
    "case_id" int4,
    "views" int4,
    "url" text,
    "case_number_in_content" _text,
    "trademarks_reg_nos" _text,
    "copyright_reg_nos" _text,
    "patent_reg_nos" _text,
    "artist_url" text,
    "source_website" text,
    "scraped_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY ("id")
);

-- Table Definition
CREATE TABLE "public"."cn_websites_files" (
    "id" int4 NOT NULL DEFAULT nextval('cn_websites_files_id_seq'::regclass),
    "filename" text,
    "cn_websites_id" int4,
    "type" text,
    "reg_no" text,
    "created_at" timestamptz DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "cn_websites_files_cn_websites_id_fkey" FOREIGN KEY ("cn_websites_id") REFERENCES "public"."cn_websites"("id"),
    PRIMARY KEY ("id")
);

Note that tb_case and tb_plaintiff are in another database. To get the table (do it once on app start): get_table_

from utils.db_utils import get_table_from_GZ

case_df=get_table_from_GZ("tb_case", force_refresh=True)

plaintiff_df=get_table_from_GZ("tb_plaintiff", force_refresh=True)

CREATE TABLE `tb_case` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `plaintiff_id` bigint DEFAULT NULL COMMENT '原告Id',
  `plaintiff_names` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '原告名称',
  `plaintiff_lawyers` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '原告律所',
  `defendant_names` text COLLATE utf8mb4_unicode_ci COMMENT '被告姓名',
  `defendant_lawyers` text COLLATE utf8mb4_unicode_ci COMMENT '美国律师',
  `docket` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '案件号',
  `court` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '法院',
  `nature_of_suit` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '案件类型',
  `nos_description` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '案件类型描述',
  `class_code` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '案件状态',
  `date_filed` date NOT NULL COMMENT '起诉日期',
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '相关介绍',
  `assigned_to` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '法官名称',
  `cause` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '诉讼理由',
  `statute` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '法令',
  `images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '图片',
  `images_status` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
  `validation_status` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `aisummary` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '摘要',
  `file_status` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '文件状态',
  `demand_amount` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '需求量',
  `date_checked` datetime DEFAULT NULL COMMENT '上一次检查更新的时间',
  `date_updated` datetime DEFAULT NULL COMMENT '最后一次更新的时间',
  `closed` date DEFAULT NULL COMMENT '关闭日期',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  `ln_url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `id` (`id`) USING BTREE,
  KEY `docket` (`docket`) USING BTREE,
  KEY `date_filed` (`date_filed`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=14892 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='案件表'

CREATE TABLE `tb_plaintiff` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `plaintiff_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原告姓名',
  `plaintiff_overview` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '原告概述',
  `plaintiff_images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '图片',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '状态',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `id` (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2384 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='原告信息表'

## 8) Types & Methods: fetch existing values and that is the list

## 9) Behaviors & Business Rules

### 9.1 Move (cn_websites_files → copyrights_files)

This action does not remove the file from cn_websites_files, it just adds it to the copyrights_files.*

Here are the database values:

* type: null
* method: "CnWebRaw"
* `reg_no: keep the one in cn_websites_files. If it is not set, generate a MD one  (use generate_md_registration_number(plaintiff_id))`
* filename: {reg_no}_{method}.webp
* production: False

Here is how to handle the files:

* save the file as {reg_no}_{method}.webp in /documents/case files/sanitize_name(docket_formated_)/images
* Look for the image on GoogleImage, TinEye and if not found use GenAI  (see code in backend\ImageSearch\ChineseWebsites_C_copyright.py)
* upload the file to COS at http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id}/high/{filename} and a smaller version (max xxxx) at http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id}/low/{filename}
* Add the key to row["images"]["copyrights"] where row is the row in case_df for the case_id. The key is the filename, the value is {"reg_no":  ...., "full_filename":  filename.replace(".webp", "_full.webp")}

Then update tb_case based on what has changed in case_df

### 9.2 Reg Number Allocation Tool

* Data source: all `<span>copyrights</span>` for current `<span>plaintiff_id</span>` with columns: `<span>reg_no</span>`, `<span>title</span>`.
* UX: searchable dropdown with `<span>reg_no – title</span>` rows. But I am concerned that the side panned is narrow. Make sure the text wraps if too long.
* On update:

  * For each row in tb_case where the old filename was a key of row["images"]["copyrights"]:
    * change the file name to {reg_no}_{method}.webp_ in _/documents/case files/sanitize_name(docket_)/images
    * also rename the certificate file from old_filename.repalce(".webp", "_full.webp") by new_filename.repalce(".webp", "_full.webp")
    * change the key and the full_filename inside row["images"]["copyrights"]
    * upload the new files to the COS: high and low folder for the image and only high folder for the certificate, e.g. http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images/{plaintiff_id}/high/{filename}
    * update the copyrights_files table
  * update the tb_case table

## 11) API Contracts (REST, indicative)

**List by plaintiff**

* The current call is api/v1/copyright?page=1&per_page=50  This will need to change to api/v1/copyright?page=1&per_page=5 (standard 5 plaintiff per page) and the return will need to be be  `<span>[{ plaintiff: { id, name, counts }, copyrights_files: [...], cn_websites_files: [...] }]</span>`
* Only include plaintiff that are present in copyrights table or in cn_websites (but you need to lookup the plaintiff_id in case_df/tb_case => create a joint table)

**Move**

* `<span>POST /api/v1/copyright/cn-websites-files/move</span>` body: `<span>{ ids: number[], defaults?: { type?, method?, reg_no? }, archive_source?: boolean }</span>`
  * Returns mapping: `<span>{ moved: [{ source_id, target_copyrights_file_id }], skipped: [] }</span>`

**Bulk (copyrights_files)**

* `<span>POST /api`/v1/copyright/copyrights-files/bulk` body supports ops:
  * `<span>{ op: "set_production", value: true|false, ids: number[] }</span>`
  * `<span>{ op: "set_type", value: string, ids: number[] }</span>`

**Split**

* `<span>POST /api/v1/copyright/copyrights-files/:id/split</span>` body: `<span>{ regions: [{x,y,w,h}], inherit: { reg_no?: boolean, type?: boolean, production?: boolean } }</span>`
  * Returns: `Success or failure. Upon success we need to refresh the view for the slipped images to show. `

**Catalogs**

* `<span>GET /api`/v1/copyright/`catalog/methods</span>`
* `<span>GET /api/v1/copyright/`catalog/types`

**Reg No list per plaintiff**

* `<span>GET /api/v1/copyright/`plaintiffs/:id/registrations `→<span>`[ { reg_no, title } ]`

## 12) Front‑End Requirements

* **State**: per‑plaintiff expansion state; per‑subsection selection set; global selection cleared on subsection switch.
* **Empty states**: for subsections with no items; for missing certificate.
* **Loading/Skeletons**: show per subsection.
* **Error handling**: inline toasts; retries where safe.

## 13) Performance

* Use server‑side pagination (e.g., 5 plaintiffs/page) with pages (current behaviour))
* Consider precomputing `<span>sort_key_reg_no</span>` for efficient sorting.
