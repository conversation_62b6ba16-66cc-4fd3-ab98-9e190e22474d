#!/usr/bin/env python3
"""
Simple Flask app runner for the Model Test Workbench backend.
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend import create_app

app = create_app()

if __name__ == '__main__':
    # app.run(debug=True, use_reloader=False, port=5000, host='0.0.0.0')
    # PyCharm's debugger and Flask's reloader can conflict.
    # This checks if a debugger is active and disables the reloader if so.
    app.run(port=5000, host='0.0.0.0', use_reloader=False, reloader_type='stat')