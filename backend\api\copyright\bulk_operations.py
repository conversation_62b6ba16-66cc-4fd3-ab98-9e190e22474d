"""
Bulk operations for copyright assets
"""
from flask import request, jsonify, current_app
from sqlalchemy import text

from backend.extensions import db

def bulk_update_copyright_assets():
    """Performs bulk actions like approve, unapprove, delete, or set type directly on external DB."""
    engine = db.get_engine(bind='maidalv_db')
    conn = None
    try:
        conn = engine.connect()
        trans = conn.begin()
        
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "No data provided"}), 400
        
        asset_ids = data.get('asset_ids', [])
        action = data.get('action')
        payload = data.get('payload', {})
        
        if not asset_ids or not action:
            return jsonify({"success": False, "error": "Missing asset_ids or action"}), 400
        
        updated_count = 0
        
        if action == 'approve':
            result = conn.execute(
                text("UPDATE copyrights SET production = true, update_time = NOW() WHERE id = ANY(:ids)"),
                {"ids": asset_ids}
            )
            updated_count = result.rowcount
            
        elif action == 'unapprove':
            result = conn.execute(
                text("UPDATE copyrights SET production = false, update_time = NOW() WHERE id = ANY(:ids)"),
                {"ids": asset_ids}
            )
            updated_count = result.rowcount
            
        elif action == 'delete':
            result = conn.execute(
                text("UPDATE copyrights SET deleted = true, update_time = NOW() WHERE id = ANY(:ids)"),
                {"ids": asset_ids}
            )
            updated_count = result.rowcount
            
        elif action == 'set_type':
            type_ids = payload.get('type_ids', [])
            if not type_ids:
                return jsonify({"success": False, "error": "No type_ids provided for set_type action"}), 400
            
            # For simplicity, use the first type_id as the type value
            type_value = type_ids[0] if type_ids else None
            result = conn.execute(
                text("UPDATE copyrights SET type = :type_value, update_time = NOW() WHERE id = ANY(:ids)"),
                {"type_value": type_value, "ids": asset_ids}
            )
            updated_count = result.rowcount
            
        else:
            return jsonify({"success": False, "error": f"Unknown action: {action}"}), 400
        
        trans.commit()
        return jsonify({"success": True, "data": {"updated_count": updated_count}})
        
    except Exception as e:
        if conn and trans:
            trans.rollback()
        current_app.logger.error(f"Error in bulk update: {e}")
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if conn:
            conn.close()

def bulk_update_copyrights_files():
    """Bulk actions for copyrights_files."""
    try:
        data = request.get_json()
        op = data.get('op')
        ids = data.get('ids', [])
        
        if not op or not ids:
            return jsonify({"success": False, "error": "Missing operation or IDs"}), 400
        
        engine = db.get_engine(bind='maidalv_db')
        with engine.connect() as conn:
            trans = conn.begin()
            try:
                if op == 'set_production':
                    value = data.get('value', False)
                    conn.execute(
                        text("UPDATE copyrights_files SET production = :value WHERE id = ANY(:ids)"),
                        {"value": value, "ids": ids}
                    )
                elif op == 'set_type':
                    value = data.get('value')
                    if not value:
                        return jsonify({"success": False, "error": "Type value required"}), 400
                    conn.execute(
                        text("UPDATE copyrights_files SET type = :value WHERE id = ANY(:ids)"),
                        {"value": value, "ids": ids}
                    )
                else:
                    return jsonify({"success": False, "error": "Invalid operation"}), 400
                
                trans.commit()
                
            except Exception as e:
                trans.rollback()
                raise e
        
        return jsonify({"success": True, "data": {"updated_count": len(ids)}})
        
    except Exception as e:
        current_app.logger.error(f"Error in bulk update copyrights files: {e}")
        return jsonify({"success": False, "error": str(e)}), 500
