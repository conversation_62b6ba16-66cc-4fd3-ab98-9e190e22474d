"""
CN Websites functionality for copyright assets
"""
import os
import uuid
from flask import request, jsonify, current_app, send_file
from sqlalchemy import text

from backend.extensions import db
from backend.utils.cache_utils import get_case_df
from .helpers import sanitize_name, generate_md_registration_number
from .file_processing import process_cn_website_file_move

def get_cn_websites_file_image(file_id):
    """Serve CN websites file images from local storage."""
    try:
        engine = db.get_engine(bind='maidalv_db')
        with engine.connect() as conn:
            # Get file info with joined cn_websites data
            row = conn.execute(
                text("""
                    SELECT cwf.filename, cw.source_website, cw.docket_formated
                    FROM cn_websites_files cwf
                    JOIN cn_websites cw ON cwf.cn_websites_id = cw.id
                    WHERE cwf.id = :id
                """),
                {"id": file_id}
            ).first()
            
            if not row:
                return jsonify({"success": False, "error": "File not found"}), 404
            
            filename = row[0]
            source_website = row[1]
            docket_formatted = row[2]
            
            if not filename:
                return jsonify({"success": False, "error": "No filename available"}), 404
            
            # Construct file path: /Documents/IP/{source_website}/sanitize_name(docket_formated)
            if os.name == 'nt':
                base_path = f"D:\\Documents\\Programing\\TRO\\Documents\\IP\\{source_website}\\{sanitize_name(docket_formatted)}"
            else:
                base_path = f"/Documents/IP/{source_website}/{sanitize_name(docket_formatted)}"
            file_path = os.path.join(base_path, filename)
            
            if not file_path:
                return jsonify({"success": False, "error": f"File not found in expected locations"}), 404
            
            return send_file(file_path)
            
    except Exception as e:
        current_app.logger.error(f"Error serving CN websites file image: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

def move_cn_websites_files():
    """Move CN websites files to copyrights_files."""
    try:
        data = request.get_json()
        ids = data.get('ids', [])
        
        if not ids:
            return jsonify({"success": False, "error": "No file IDs provided"}), 400
        
        moved = []
        skipped = []
        
        engine = db.get_engine(bind='maidalv_db')
        case_df = get_case_df()
        
        with engine.connect() as conn:
            trans = conn.begin()
            try:
                for file_id in ids:
                    # Get CN websites file info
                    cn_file_row = conn.execute(
                        text("""
                            SELECT cwf.id, cwf.filename, cwf.type, cwf.reg_no, cwf.cn_websites_id,
                                   cw.case_id, cw.docket_formated, cw.source_website
                            FROM cn_websites_files cwf
                            JOIN cn_websites cw ON cwf.cn_websites_id = cw.id
                            WHERE cwf.id = :id
                        """),
                        {"id": file_id}
                    ).first()
                    
                    if not cn_file_row:
                        skipped.append({"source_id": file_id, "reason": "File not found"})
                        continue
                    
                    # Get plaintiff_id from case
                    case_id = cn_file_row[5]
                    if case_df.empty:
                        skipped.append({"source_id": file_id, "reason": "Case data not available"})
                        continue
                    
                    case_row = case_df[case_df['id'] == case_id]
                    if case_row.empty:
                        skipped.append({"source_id": file_id, "reason": "Case not found"})
                        continue
                    
                    plaintiff_id = case_row.iloc[0]['plaintiff_id']
                    reg_no = cn_file_row[3]
                    source_filename = cn_file_row[1]
                    source_website = cn_file_row[7]
                    docket_formatted = cn_file_row[6]
                    
                    # Generate MD registration number if needed
                    if not reg_no:
                        reg_no = generate_md_registration_number(plaintiff_id)
                    
                    # Create new copyrights_files entry
                    new_filename = f"{reg_no}_CnWebRaw.webp"
                    
                    new_id = conn.execute(
                        text("""
                            INSERT INTO copyrights_files (filename, registration_number, method, production, type)
                            VALUES (:filename, :reg_no, 'CnWebRaw', false, null)
                            RETURNING id
                        """),
                        {
                            "filename": new_filename,
                            "reg_no": reg_no
                        }
                    ).scalar_one()
                    
                    # Process the file (copy, resize, upload to COS, etc.)
                    try:
                        process_cn_website_file_move(
                            source_filename=source_filename,
                            source_website=source_website,
                            docket_formatted=docket_formatted,
                            new_filename=new_filename,
                            reg_no=reg_no,
                            plaintiff_id=plaintiff_id,
                            case_id=case_id
                        )
                        
                        moved.append({
                            "source_id": file_id,
                            "target_copyrights_file_id": str(new_id)
                        })
                    except Exception as e:
                        current_app.logger.error(f"Error processing file move for {file_id}: {e}")
                        skipped.append({
                            "source_id": file_id, 
                            "reason": f"File processing failed: {str(e)}"
                        })
                
                trans.commit()
                
            except Exception as e:
                trans.rollback()
                raise e
        
        return jsonify({
            "success": True,
            "data": {
                "moved": moved,
                "skipped": skipped
            }
        })
        
    except Exception as e:
        current_app.logger.error(f"Error moving CN websites files: {e}")
        return jsonify({"success": False, "error": str(e)}), 500
