"""
Core CRUD operations for copyright assets
"""
import uuid
import pandas as pd
from flask import Blueprint, request, jsonify, current_app
from sqlalchemy import text

from backend.extensions import db
from backend.utils.db_utils import get_copyright_data_as_dataframe
from backend.utils.file_utils import save_uploaded_file
from backend.utils.cache_utils import get_plaintiff_df, get_case_df
from .helpers import (
    get_low_res_path, get_high_res_path, get_certificate_path, 
    _sort_by_reg_no, generate_md_registration_number
)

def _get_copyrights_files_for_plaintiff(plaintiff_id, filters=None):
    """Get copyrights_files data for a specific plaintiff with optional filters."""
    engine = db.get_engine(bind='maidalv_db')
    with engine.connect() as conn:
        # Build the base query
        query = """
            SELECT cf.id, cf.filename, cf.registration_number as reg_no, cf.method, cf.production, cf.type,
                   cf.create_time, cf.update_time, c.certificate_status
            FROM copyrights_files cf
            JOIN copyrights c ON c.registration_number = cf.registration_number
            WHERE c.plaintiff_id = :plaintiff_id
        """
        params = {"plaintiff_id": plaintiff_id}

        # Apply filters if provided
        if filters:
            if filters.get('registration_number'):
                query += " AND cf.registration_number ILIKE :reg_no"
                params['reg_no'] = f"%{filters['registration_number']}%"

            if filters.get('method'):
                query += " AND cf.method = :method"
                params['method'] = filters['method']

            if filters.get('type'):
                query += " AND cf.type = :type"
                params['type'] = filters['type']

            if filters.get('production'):
                if filters['production'] == 'true':
                    query += " AND cf.production = true"
                elif filters['production'] == 'false':
                    query += " AND cf.production = false"

            if filters.get('certificate_status'):
                query += " AND c.certificate_status = :cert_status"
                params['cert_status'] = filters['certificate_status']

        rows = conn.execute(text(query), params).fetchall()
        
        files = []
        for row in rows:
            files.append({
                "id": str(row[0]),
                "filename": row[1],
                "reg_no": row[2],
                "method": row[3],
                "production": bool(row[4]) if row[4] is not None else False,
                "type": row[5],
                "create_time": row[6].isoformat() if row[6] else None,
                "update_time": row[7].isoformat() if row[7] else None,
                "certificate_status": row[8],
                "low_res_path": get_low_res_path(plaintiff_id, row[2], row[3]),
                "high_res_path": get_high_res_path(plaintiff_id, row[2], row[3]),
                "certificate_path": get_certificate_path(plaintiff_id, row[2], row[3])
            })

        return _sort_by_reg_no(files)

def _get_cn_websites_files_for_plaintiff(plaintiff_id, filters=None):
    """Get cn_websites_files data for a specific plaintiff with optional filters."""
    engine = db.get_engine(bind='maidalv_db')
    case_df = get_case_df()

    # Get case_ids for this plaintiff
    if case_df.empty:
        return []

    case_ids = case_df[case_df['plaintiff_id'] == plaintiff_id]['id'].tolist()
    if not case_ids:
        return []

    with engine.connect() as conn:
        # Build the base query
        query = """
            SELECT cwf.id, cwf.filename, cwf.type, cwf.reg_no, cwf.created_at,
                   cw.docket_in_title, cw.docket_formated, cw.case_id, cw.url,
                   cw.source_website, cw.posting_date
            FROM cn_websites_files cwf
            JOIN cn_websites cw ON cwf.cn_websites_id = cw.id
            WHERE cw.case_id = ANY(:case_ids)
        """
        params = {"case_ids": case_ids}

        # Apply filters if provided
        if filters:
            if filters.get('registration_number'):
                query += " AND cwf.reg_no ILIKE :reg_no"
                params['reg_no'] = f"%{filters['registration_number']}%"

            if filters.get('type'):
                query += " AND cwf.type = :type"
                params['type'] = filters['type']

        rows = conn.execute(text(query), params).fetchall()
        
        files = []
        for row in rows:
            files.append({
                "id": row[0],
                "filename": row[1],
                "type": row[2],
                "reg_no": row[3],
                "created_at": row[4].isoformat() if row[4] else None,
                "docket_in_title": row[5],
                "docket_formatted": row[6],
                "case_id": row[7],
                "url": row[8],
                "website": row[9],
                "site_domain": row[9],  # Using source_website as site_domain
                "post_date": row[10].isoformat() if row[10] else None,
                # For cn_websites_files, we'll use a different path structure
                "low_res_path": f"/api/v1/copyright/cn-websites-files/{row[0]}/image",
                "high_res_path": f"/api/v1/copyright/cn-websites-files/{row[0]}/image"
            })
        
        return _sort_by_reg_no(files)

def get_copyright_assets():
    """
    Gets a paginated list of copyright assets grouped by plaintiff.
    Returns data in the new structure: [{ plaintiff: {...}, copyrights_files: [...], cn_websites_files: [...] }]
    """
    try:
        # --- Step 1: Parse pagination and filter arguments ---
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 5))  # 5 plaintiffs per page as per PRD

        # Parse filter arguments
        filters = {
            'plaintiff_name': request.args.get('plaintiff_name', '').strip(),
            'registration_number': request.args.get('registration_number', '').strip(),
            'method': request.args.get('method', '').strip(),
            'type': request.args.get('type', '').strip(),
            'production': request.args.get('production', '').strip(),
            'certificate_status': request.args.get('certificate_status', '').strip()
        }

        # Get plaintiff data
        plaintiff_df = get_plaintiff_df()
        if plaintiff_df.empty:
            return jsonify({"success": True, "data": {"plaintiffs": [], "pagination": {"page": page, "per_page": per_page, "total_pages": 0, "total_items": 0}}})

        # Get case data to find plaintiffs that have copyright or cn_websites data
        case_df = get_case_df()
        engine = db.get_engine(bind='maidalv_db')
        
        # Find plaintiffs that have either copyrights or cn_websites data
        with engine.connect() as conn:
            # Get plaintiff_ids that have copyrights
            copyright_plaintiff_ids = set()
            copyright_rows = conn.execute(text("SELECT DISTINCT plaintiff_id FROM copyrights WHERE plaintiff_id IS NOT NULL")).fetchall()
            copyright_plaintiff_ids.update(row[0] for row in copyright_rows)
            
            # Get plaintiff_ids that have cn_websites (via case_df)
            cn_websites_plaintiff_ids = set()
            if not case_df.empty:
                # Get case_ids that have cn_websites
                cn_case_ids = conn.execute(text("SELECT DISTINCT case_id FROM cn_websites WHERE case_id IS NOT NULL")).fetchall()
                cn_case_ids = {row[0] for row in cn_case_ids}
                
                # Map case_ids to plaintiff_ids
                for _, case_row in case_df.iterrows():
                    if case_row['id'] in cn_case_ids:
                        cn_websites_plaintiff_ids.add(case_row['plaintiff_id'])
            
            # Combine both sets
            relevant_plaintiff_ids = copyright_plaintiff_ids | cn_websites_plaintiff_ids
        
        # Filter plaintiff_df to only include relevant plaintiffs
        relevant_plaintiffs = plaintiff_df[plaintiff_df['id'].isin(relevant_plaintiff_ids)].copy()

        # Apply plaintiff name filter if provided
        if filters.get('plaintiff_name'):
            relevant_plaintiffs = relevant_plaintiffs[
                relevant_plaintiffs['plaintiff_name'].str.contains(
                    filters['plaintiff_name'], case=False, na=False
                )
            ]

        if relevant_plaintiffs.empty:
            return jsonify({"success": True, "data": {"plaintiffs": [], "pagination": {"page": page, "per_page": per_page, "total_pages": 0, "total_items": 0}}})
        
        # Paginate plaintiffs
        total_plaintiffs = len(relevant_plaintiffs)
        total_pages = (total_plaintiffs + per_page - 1) // per_page
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        page_plaintiffs = relevant_plaintiffs.iloc[start_idx:end_idx]

        # Build plaintiff data with their files
        plaintiffs_data = []
        for _, plaintiff_row in page_plaintiffs.iterrows():
            plaintiff_id = plaintiff_row['id']
            plaintiff_name = plaintiff_row['plaintiff_name']
            
            # Get copyrights_files and cn_websites_files for this plaintiff
            copyrights_files = _get_copyrights_files_for_plaintiff(plaintiff_id, filters)
            cn_websites_files = _get_cn_websites_files_for_plaintiff(plaintiff_id, filters)
            
            # Count totals
            copyrights_count = len(copyrights_files)
            cn_websites_count = len(cn_websites_files)
            
            plaintiffs_data.append({
                "plaintiff": {
                    "id": plaintiff_id,
                    "name": plaintiff_name,
                    "counts": {
                        "copyrights_files": copyrights_count,
                        "cn_websites_files": cn_websites_count,
                        "total": copyrights_count + cn_websites_count
                    }
                },
                "copyrights_files": copyrights_files,
                "cn_websites_files": cn_websites_files
            })

        return jsonify({"success": True, "data": {
            "plaintiffs": plaintiffs_data,
            "pagination": {
                "page": page,
                "per_page": per_page,
                "total_pages": total_pages,
                "total_items": total_plaintiffs
            }
        }})

    except Exception as e:
        current_app.logger.error(f"Error getting copyright assets: {e}")
        return jsonify({"success": False, "error": str(e)}), 500

def add_copyright_asset():
    """Adds a new copyright asset: uploads file, inserts into external copyrights, no local viz/types."""
    engine = db.get_engine(bind='maidalv_db')
    conn = None
    try:
        conn = engine.connect()
        trans = conn.begin()

        # Parse form data
        registration_number = request.form.get('registration_number')
        plaintiff_id = request.form.get('plaintiff_id')
        method = request.form.get('method', 'manual')

        if not registration_number:
            return jsonify({"success": False, "error": "Registration number is required"}), 400

        # Handle file upload
        if 'file' not in request.files:
            return jsonify({"success": False, "error": "No file provided"}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({"success": False, "error": "No file selected"}), 400

        # Save uploaded file
        saved_path = save_uploaded_file(file, 'copyright_uploads')
        if not saved_path:
            return jsonify({"success": False, "error": "Failed to save uploaded file"}), 500

        # Insert into external copyrights table
        new_id = str(uuid.uuid4())
        conn.execute(
            text("""
                INSERT INTO copyrights (id, registration_number, plaintiff_id, method, create_time, update_time)
                VALUES (:id, :reg_no, :plaintiff_id, :method, NOW(), NOW())
            """),
            {
                "id": new_id,
                "reg_no": registration_number,
                "plaintiff_id": plaintiff_id,
                "method": method
            }
        )

        trans.commit()

        return jsonify({
            "success": True,
            "data": {
                "id": new_id,
                "registration_number": registration_number,
                "plaintiff_id": plaintiff_id,
                "method": method,
                "file_path": saved_path
            }
        })

    except Exception as e:
        if conn and trans:
            trans.rollback()
        current_app.logger.error(f"Error adding copyright asset: {e}")
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if conn:
            conn.close()

def update_copyright_asset(id):
    """Updates a single copyright asset's details directly in external DB. ID is copyrights.id (UUID)."""
    engine = db.get_engine(bind='maidalv_db')
    conn = None
    try:
        conn = engine.connect()
        trans = conn.begin()

        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "No data provided"}), 400

        # Build update query dynamically
        update_fields = []
        params = {"id": id}

        allowed_fields = ['registration_number', 'method', 'production', 'type', 'certificate_status']
        for field in allowed_fields:
            if field in data:
                update_fields.append(f"{field} = :{field}")
                params[field] = data[field]

        if not update_fields:
            return jsonify({"success": False, "error": "No valid fields to update"}), 400

        # Add update timestamp
        update_fields.append("update_time = NOW()")

        query = f"UPDATE copyrights SET {', '.join(update_fields)} WHERE id = :id"
        result = conn.execute(text(query), params)

        if result.rowcount == 0:
            return jsonify({"success": False, "error": "Asset not found"}), 404

        trans.commit()
        return jsonify({"success": True, "data": {"updated_fields": list(data.keys())}})

    except Exception as e:
        if conn and trans:
            trans.rollback()
        current_app.logger.error(f"Error updating copyright asset id={id}: {e}")
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if conn and not conn.closed:
            conn.close()
