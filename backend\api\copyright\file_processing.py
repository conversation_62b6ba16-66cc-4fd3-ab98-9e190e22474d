"""
File processing operations for copyright assets
"""
import os
import shutil
import asyncio
from datetime import datetime
from flask import current_app

from backend.utils.image_resize import create_resized_image
from backend.utils.Tencent_COS import get_cos_client
from backend.utils.cache_utils import get_case_df
from .helpers import sanitize_name
from .image_search import search_and_process_image

def process_cn_website_file_move(source_filename, source_website, docket_formatted, 
                                new_filename, reg_no, plaintiff_id, case_id):
    """
    Process moving a CN website file to copyrights_files.
    This includes file copying, resizing, COS upload, and image search.
    """
    try:
        # Get case data for date_filed
        case_df = get_case_df()
        case_row = case_df[case_df['id'] == case_id]
        if case_row.empty:
            raise ValueError(f"Case {case_id} not found")
        
        # Get date_filed and docket for directory structure
        date_filed = case_row.iloc[0].get('date_filed')
        docket = case_row.iloc[0].get('docket', docket_formatted)
        
        # Format date for directory name
        if date_filed:
            if isinstance(date_filed, str):
                date_filed_str = date_filed
            else:
                date_filed_str = date_filed.strftime('%Y-%m-%d')
        else:
            date_filed_str = "unknown-date"
        
        # Source file path
        if os.name == 'nt':
            source_path = f"D:\\Documents\\Programing\\TRO\\Documents\\IP\\{source_website}\\{sanitize_name(docket_formatted)}"
        else:
            source_path = f"/Documents/IP/{source_website}/{sanitize_name(docket_formatted)}"
        source_file_path = None
        
        # Try to find the source file
        possible_source_paths = [
            os.path.join(source_path, source_filename),
            os.path.join(source_path, "images", source_filename),
            os.path.join(source_path, "images", "high", source_filename),
            os.path.join(source_path, "images", "low", source_filename)
        ]
        
        for path in possible_source_paths:
            if os.path.exists(path):
                source_file_path = path
                break
        
        if not source_file_path:
            raise FileNotFoundError(f"Source file {source_filename} not found in {source_path}")
        
        # Destination directory structure
        if os.name == 'nt':
            dest_dir = f"D:\\Documents\\Programing\\TRO\\Documents\\Case Files\\{sanitize_name(f'{date_filed_str} - {docket}')}\\images"
        else:
            dest_dir = f"/Documents/Case Files/{sanitize_name(f'{date_filed_str} - {docket}')}/images"
        os.makedirs(dest_dir, exist_ok=True)
        
        # Copy source file to destination
        dest_file_path = os.path.join(dest_dir, new_filename)
        shutil.copy2(source_file_path, dest_file_path)
        
        # Create resized versions (high and low)
        # The create_resized_image function expects the output_path to be the parent of /images
        output_path = dest_dir.replace('/images', '')
        resized_filename = create_resized_image(dest_dir, new_filename, output_path)

        if not resized_filename:
            current_app.logger.warning(f"Failed to create resized images for {new_filename}")
            resized_filename = new_filename
        
        # Upload to COS
        upload_to_cos(plaintiff_id, resized_filename, output_path)
        
        # Perform image search and processing
        search_and_process_image(dest_file_path, reg_no, plaintiff_id)
        
        # Update case images metadata
        update_case_images_metadata(case_id, reg_no, resized_filename)
        
        current_app.logger.info(f"Successfully processed CN website file move: {source_filename} -> {new_filename}")
        
    except Exception as e:
        current_app.logger.error(f"Error processing CN website file move: {e}")
        raise

def upload_to_cos(plaintiff_id, filename, local_base_dir):
    """Upload files to Tencent COS."""
    try:
        client, bucket = get_cos_client()

        # Upload high resolution image
        high_path = os.path.join(local_base_dir, "images", "high", filename)
        if os.path.exists(high_path):
            cos_key_high = f"plaintiff_images/{plaintiff_id}/high/{filename}"
            with open(high_path, 'rb') as f:
                client.put_object(
                    Bucket=bucket,
                    Key=cos_key_high,
                    Body=f.read()
                )
            current_app.logger.info(f"Uploaded to COS: {cos_key_high}")

        # Upload low resolution image
        low_path = os.path.join(local_base_dir, "images", "low", filename)
        if os.path.exists(low_path):
            cos_key_low = f"plaintiff_images/{plaintiff_id}/low/{filename}"
            with open(low_path, 'rb') as f:
                client.put_object(
                    Bucket=bucket,
                    Key=cos_key_low,
                    Body=f.read()
                )
            current_app.logger.info(f"Uploaded to COS: {cos_key_low}")

        # Upload certificate if exists
        cert_filename = filename.replace('.webp', '_full.webp')
        cert_path = os.path.join(local_base_dir, "images", "high", cert_filename)
        if os.path.exists(cert_path):
            cos_key_cert = f"plaintiff_images/{plaintiff_id}/high/{cert_filename}"
            with open(cert_path, 'rb') as f:
                client.put_object(
                    Bucket=bucket,
                    Key=cos_key_cert,
                    Body=f.read()
                )
            current_app.logger.info(f"Uploaded certificate to COS: {cos_key_cert}")

    except Exception as e:
        current_app.logger.error(f"Error uploading to COS: {e}")
        raise

def update_case_images_metadata(case_id, reg_no, filename):
    """Update case images metadata in tb_case."""
    try:
        # This would update the case_df and then sync to tb_case
        # For now, we'll log the action
        current_app.logger.info(f"Would update case {case_id} images metadata: {reg_no} -> {filename}")
        
        # TODO: Implement actual case metadata update
        # This requires updating the case_df and then syncing back to tb_case
        # The structure should be: row["images"]["copyrights"][filename] = {"reg_no": reg_no, "full_filename": filename.replace(".webp", "_full.webp")}
        
    except Exception as e:
        current_app.logger.error(f"Error updating case images metadata: {e}")
        raise

def handle_reg_number_change(old_reg_no, new_reg_no, plaintiff_id, method):
    """Handle file renaming when registration number changes."""
    try:
        # Get case data to find affected cases
        case_df = get_case_df()
        
        old_filename = f"{old_reg_no}_{method}.webp"
        new_filename = f"{new_reg_no}_{method}.webp"
        old_cert_filename = f"{old_reg_no}_{method}_full.webp"
        new_cert_filename = f"{new_reg_no}_{method}_full.webp"
        
        # Find cases that reference this file
        affected_cases = []
        for _, case_row in case_df.iterrows():
            if case_row['plaintiff_id'] == plaintiff_id:
                images = case_row.get('images', {})
                if isinstance(images, dict):
                    copyrights = images.get('copyrights', {})
                    if isinstance(copyrights, dict) and old_filename in copyrights:
                        affected_cases.append(case_row)
        
        # Process each affected case
        for case_row in affected_cases:
            docket = case_row.get('docket', '')
            date_filed = case_row.get('date_filed')
            
            if date_filed:
                if isinstance(date_filed, str):
                    date_filed_str = date_filed
                else:
                    date_filed_str = date_filed.strftime('%Y-%m-%d')
            else:
                date_filed_str = "unknown-date"
            
            # Directory path
            if os.name == 'nt':
                case_dir = f"D:\\Documents\\Programing\\TRO\\Documents\\Case Files\\{sanitize_name(f'{date_filed_str} - {docket}')}\\images"
            else:
                case_dir = f"/Documents/Case Files/{sanitize_name(f'{date_filed_str} - {docket}')}/images"
            
            # Rename files locally
            rename_local_files(case_dir, old_filename, new_filename, old_cert_filename, new_cert_filename)
            
            # Update COS files
            rename_cos_files(plaintiff_id, old_filename, new_filename, old_cert_filename, new_cert_filename)
            
            # Update case metadata
            update_case_metadata_for_rename(case_row['id'], old_filename, new_filename, new_reg_no, new_cert_filename)
        
        current_app.logger.info(f"Successfully handled reg number change: {old_reg_no} -> {new_reg_no}")
        
    except Exception as e:
        current_app.logger.error(f"Error handling reg number change: {e}")
        raise

def rename_local_files(case_dir, old_filename, new_filename, old_cert_filename, new_cert_filename):
    """Rename files in local directory structure."""
    try:
        # case_dir should be the images directory, so we need high and low subdirs
        # Rename main image files
        for subdir in ['high', 'low']:
            old_path = os.path.join(case_dir, subdir, old_filename)
            new_path = os.path.join(case_dir, subdir, new_filename)
            if os.path.exists(old_path):
                os.rename(old_path, new_path)
                current_app.logger.info(f"Renamed local file: {old_path} -> {new_path}")

        # Rename certificate file (only in high directory)
        old_cert_path = os.path.join(case_dir, "high", old_cert_filename)
        new_cert_path = os.path.join(case_dir, "high", new_cert_filename)
        if os.path.exists(old_cert_path):
            os.rename(old_cert_path, new_cert_path)
            current_app.logger.info(f"Renamed certificate file: {old_cert_path} -> {new_cert_path}")

    except Exception as e:
        current_app.logger.error(f"Error renaming local files: {e}")
        raise

def rename_cos_files(plaintiff_id, old_filename, new_filename, old_cert_filename, new_cert_filename):
    """Rename files in Tencent COS."""
    try:
        client, bucket = get_cos_client()
        
        # Rename main image files
        for subdir in ['high', 'low']:
            old_key = f"plaintiff_images/{plaintiff_id}/{subdir}/{old_filename}"
            new_key = f"plaintiff_images/{plaintiff_id}/{subdir}/{new_filename}"
            
            try:
                # Copy to new key
                client.copy_object(
                    Bucket=bucket,
                    Key=new_key,
                    CopySource={'Bucket': bucket, 'Key': old_key, 'Region': 'ap-guangzhou'}
                )
                # Delete old key
                client.delete_object(Bucket=bucket, Key=old_key)
                current_app.logger.info(f"Renamed COS file: {old_key} -> {new_key}")
            except Exception as e:
                current_app.logger.warning(f"Could not rename COS file {old_key}: {e}")
        
        # Rename certificate file
        old_cert_key = f"plaintiff_images/{plaintiff_id}/high/{old_cert_filename}"
        new_cert_key = f"plaintiff_images/{plaintiff_id}/high/{new_cert_filename}"
        
        try:
            client.copy_object(
                Bucket=bucket,
                Key=new_cert_key,
                CopySource={'Bucket': bucket, 'Key': old_cert_key, 'Region': 'ap-guangzhou'}
            )
            client.delete_object(Bucket=bucket, Key=old_cert_key)
            current_app.logger.info(f"Renamed COS certificate: {old_cert_key} -> {new_cert_key}")
        except Exception as e:
            current_app.logger.warning(f"Could not rename COS certificate {old_cert_key}: {e}")
            
    except Exception as e:
        current_app.logger.error(f"Error renaming COS files: {e}")
        raise

def update_case_metadata_for_rename(case_id, old_filename, new_filename, new_reg_no, new_cert_filename):
    """Update case metadata after file rename."""
    try:
        # TODO: Implement actual case metadata update
        # This requires updating the case_df and then syncing back to tb_case
        current_app.logger.info(f"Would update case {case_id} metadata: {old_filename} -> {new_filename}")
        
    except Exception as e:
        current_app.logger.error(f"Error updating case metadata for rename: {e}")
        raise
