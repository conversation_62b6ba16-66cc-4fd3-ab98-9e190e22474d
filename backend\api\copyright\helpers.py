"""
Helper functions and utilities for copyright API
"""
import os
import uuid
import requests
import numpy as np
from flask import current_app
from sqlalchemy import text
from backend.AI.shared_models import get_siglip_model, EMBEDDING_VECTOR_SIZE

# Constants
COS_BASE_URL = "http://troimages-1329604052.cos.ap-guangzhou.myqcloud.com/plaintiff_images"
QDRANT_COLLECTION_NAME = "IP_Assets_Optimized"
QDRANT_VECTOR_SIZE = EMBEDDING_VECTOR_SIZE

def get_low_res_path(plaintiff_id, registration_number, method):
    """Gets the full URL for a low-res image. Filename: registration_number_method.webp"""
    if not plaintiff_id or not registration_number:
        return None
    return f"{COS_BASE_URL}/{plaintiff_id}/low/{registration_number}_{method}.webp"

def get_high_res_path(plaintiff_id, registration_number, method):
    """Gets the full URL for a high-res image. Filename: registration_number_method.webp"""
    if not plaintiff_id or not registration_number:
        return None
    return f"{COS_BASE_URL}/{plaintiff_id}/high/{registration_number}_{method}.webp"

def get_certificate_path(plaintiff_id, registration_number, method):
    """Gets the full URL for a certificate image. Filename: registration_number_method_full.webp"""
    if not plaintiff_id or not registration_number:
        return None
    return f"{COS_BASE_URL}/{plaintiff_id}/high/{registration_number}_{method}_full.webp"

def generate_md_registration_number(plaintiff_id):
    """Generate a unique MD registration number for a plaintiff."""
    return f"MD{str(uuid.uuid4())[:8]}"

def _sort_by_reg_no(items, reg_no_key='reg_no'):
    """Sort items by registration number, putting MD-prefixed ones last."""
    def sort_key(item):
        reg_no = item.get(reg_no_key, '') or ''
        if reg_no.startswith('MD'):
            return (1, reg_no)  # MD goes last
        return (0, reg_no)  # Others go first
    return sorted(items, key=sort_key)

def _url_to_cos_key(url: str) -> str:
    return url.split(".myqcloud.com/")[-1] if url else None

def _fetch_asset_row(engine, cid: str):
    with engine.connect() as c2:
        row = c2.execute(
            text("SELECT registration_number, method, plaintiff_id, production FROM copyrights WHERE id = :id"),
            {"id": cid}
        ).first()
    if not row:
        return None
    return {
        "registration_number": row[0],
        "method": row[1],
        "plaintiff_id": row[2],
        "production": bool(row[3]) if row[3] is not None else False
    }

def _high_res_url(plaintiff_id, reg_no, method):
    return get_high_res_path(plaintiff_id, reg_no, method)

def _low_res_url(plaintiff_id, reg_no, method):
    return get_low_res_path(plaintiff_id, reg_no, method)

def _download_image_bytes(url: str) -> bytes | None:
    if not url:
        return None
    try:
        resp = requests.get(url, timeout=30)
        if resp.status_code == 200:
            return resp.content
        return None
    except Exception:
        current_app.logger.exception(f"Failed to download image from {url}")
        return None

def _compute_embedding_from_bytes(img_bytes: bytes) -> list | None:
    try:
        model = get_siglip_model(load_if_needed=True)
        feats = model.compute_features([img_bytes], data_type="image")
        if isinstance(feats, np.ndarray):
            vec = feats[0] if feats.ndim >= 2 else feats
            vec_list = vec.tolist()
        else:
            # Fallback if model returns tensor-like
            vec_list = list(feats[0])
        if len(vec_list) != QDRANT_VECTOR_SIZE:
            current_app.logger.warning(f"Embedding size {len(vec_list)} != expected {QDRANT_VECTOR_SIZE}")
        return vec_list
    except Exception:
        current_app.logger.exception(f"Failed to compute embedding")
        return None

def _delete_vector_if_exists(cid: str) -> None:
    from backend.utils.vector_store import get_qdrant_client, delete_vector
    qdrant = get_qdrant_client()
    if not qdrant:
        return
    try:
        delete_vector(client=qdrant, collection_name=QDRANT_COLLECTION_NAME, point_id=str(cid))
    except Exception:
        current_app.logger.exception(f"Failed deleting vector for id={cid}")

def sanitize_name(name):
    """Sanitize a name for use in file paths"""
    if not name:
        return ""
    # Replace problematic characters
    import re
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', str(name))
    sanitized = re.sub(r'\s+', '_', sanitized)
    return sanitized.strip('_')
