"""
Image processing operations for copyright assets
"""
import io
from PIL import Image
from flask import request, jsonify, current_app, Response
from sqlalchemy import text
import requests

from backend.extensions import db
from backend.utils.Tencent_COS import get_cos_client
from .helpers import _fetch_asset_row, _url_to_cos_key

def crop_copyright_asset(id):
    """
    Server-side crop and replace:
    - Accepts JSON { x, y, width, height } in original image pixel coordinates.
    - Downloads original from COS, crops, uploads back to the SAME keys for high/low.
    """
    engine = db.get_engine(bind='maidalv_db')
    conn = None
    try:
        conn = engine.connect()
        
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "No crop data provided"}), 400
        
        x = int(data.get('x', 0))
        y = int(data.get('y', 0))
        width = int(data.get('width', 0))
        height = int(data.get('height', 0))
        
        if width <= 0 or height <= 0:
            return jsonify({"success": False, "error": "Invalid crop dimensions"}), 400
        
        # Fetch asset info
        asset_row = _fetch_asset_row(engine, id)
        if not asset_row:
            return jsonify({"success": False, "error": "Asset not found"}), 404
        
        reg_no = asset_row["registration_number"]
        method = asset_row["method"]
        plaintiff_id = asset_row["plaintiff_id"]
        
        if not all([reg_no, method, plaintiff_id]):
            return jsonify({"success": False, "error": "Asset missing required fields"}), 400
        
        # Get COS client
        cos_client, bucket = get_cos_client()
        
        # Download high-res image from COS
        high_key = f"plaintiff_images/{plaintiff_id}/high/{reg_no}_{method}.webp"
        
        try:
            response = cos_client.get_object(Bucket=bucket, Key=high_key)
            original_bytes = response['Body'].read()
        except Exception as e:
            current_app.logger.error(f"Failed to download from COS {high_key}: {e}")
            return jsonify({"success": False, "error": "Failed to download original image"}), 500
        
        # Open image and crop
        try:
            original_img = Image.open(io.BytesIO(original_bytes))
            cropped_img = original_img.crop((x, y, x + width, y + height))
            
            # Save cropped image to bytes
            cropped_bytes = io.BytesIO()
            cropped_img.save(cropped_bytes, format='WEBP', quality=95)
            cropped_bytes.seek(0)
            
        except Exception as e:
            current_app.logger.error(f"Failed to crop image: {e}")
            return jsonify({"success": False, "error": "Failed to crop image"}), 500
        
        # Upload cropped image back to COS (replace high-res)
        try:
            cos_client.put_object(
                Bucket=bucket,
                Key=high_key,
                Body=cropped_bytes.getvalue()
            )
        except Exception as e:
            current_app.logger.error(f"Failed to upload cropped image to COS: {e}")
            return jsonify({"success": False, "error": "Failed to upload cropped image"}), 500
        
        # Create and upload low-res version
        try:
            # Resize for low-res (max width 300px)
            low_img = cropped_img.copy()
            if low_img.width > 300:
                ratio = 300 / low_img.width
                new_height = int(low_img.height * ratio)
                low_img = low_img.resize((300, new_height), Image.Resampling.LANCZOS)
            
            low_bytes = io.BytesIO()
            low_img.save(low_bytes, format='WEBP', quality=80)
            low_bytes.seek(0)
            
            low_key = f"plaintiff_images/{plaintiff_id}/low/{reg_no}_{method}.webp"
            cos_client.put_object(
                Bucket=bucket,
                Key=low_key,
                Body=low_bytes.getvalue()
            )
            
        except Exception as e:
            current_app.logger.warning(f"Failed to create/upload low-res version: {e}")
            # Don't fail the whole operation for this
        
        return jsonify({"success": True, "data": {"message": "Image cropped and updated successfully"}})
        
    except Exception as e:
        current_app.logger.error(f"Error cropping copyright asset id={id}: {e}")
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if conn:
            conn.close()

def promote_copyright_asset(id):
    """Promotes an MD-prefixed asset to a real registration number (external DB only)."""
    engine = db.get_engine(bind='maidalv_db')
    conn = None
    try:
        conn = engine.connect()
        trans = conn.begin()
        
        data = request.get_json()
        if not data:
            return jsonify({"success": False, "error": "No data provided"}), 400
        
        new_reg_no = data.get('registration_number', '').strip()
        if not new_reg_no:
            return jsonify({"success": False, "error": "New registration number is required"}), 400
        
        # Fetch current asset
        asset_row = _fetch_asset_row(engine, id)
        if not asset_row:
            return jsonify({"success": False, "error": "Asset not found"}), 404
        
        old_reg_no = asset_row["registration_number"]
        if not old_reg_no or not old_reg_no.startswith('MD'):
            return jsonify({"success": False, "error": "Asset is not MD-prefixed"}), 400
        
        # Update registration number
        result = conn.execute(
            text("UPDATE copyrights SET registration_number = :new_reg_no, update_time = NOW() WHERE id = :id"),
            {"new_reg_no": new_reg_no, "id": id}
        )
        
        if result.rowcount == 0:
            return jsonify({"success": False, "error": "Failed to update asset"}), 500
        
        trans.commit()
        
        # Handle file renaming (this would be done asynchronously in production)
        try:
            from .file_processing import handle_reg_number_change
            handle_reg_number_change(
                old_reg_no, 
                new_reg_no, 
                asset_row["plaintiff_id"], 
                asset_row["method"]
            )
        except Exception as e:
            current_app.logger.error(f"Error handling file rename for promotion: {e}")
            # Don't fail the promotion for file rename issues
        
        return jsonify({"success": True, "data": {"old_reg_no": old_reg_no, "new_reg_no": new_reg_no}})
        
    except Exception as e:
        if conn and trans:
            trans.rollback()
        current_app.logger.error(f"Error promoting copyright asset id={id}: {e}")
        return jsonify({"success": False, "error": str(e)}), 500
    finally:
        if conn:
            conn.close()

def proxy_image():
    """
    Proxies an image from an external URL (like Tencent COS) to avoid CORS issues
    in the frontend, especially for canvas operations like cropping.
    """
    url = request.args.get('url')
    if not url:
        return jsonify({"success": False, "error": "No URL provided"}), 400
    
    try:
        # Fetch the image from the external URL
        response = requests.get(url, timeout=30)
        if response.status_code == 200:
            # Return the image with appropriate headers
            return Response(
                response.content,
                mimetype=response.headers.get('Content-Type', 'image/jpeg'),
                headers={
                    'Cache-Control': 'public, max-age=3600',
                    'Access-Control-Allow-Origin': '*'
                }
            )
        else:
            current_app.logger.warning(f"Failed to fetch image from {url}: HTTP {response.status_code}")
            return jsonify({"success": False, "error": f"Failed to fetch image: HTTP {response.status_code}"}), response.status_code
            
    except requests.RequestException as e:
        current_app.logger.error(f"Error fetching image from {url}: {e}")
        return jsonify({"success": False, "error": "Could not fetch image from source"}), 502

def split_copyright_file(file_id):
    """Split a copyright file into multiple regions (placeholder)."""
    try:
        data = request.get_json()
        regions = data.get('regions', [])
        inherit = data.get('inherit', {})
        
        if not regions:
            return jsonify({"success": False, "error": "No regions provided"}), 400
        
        # Placeholder implementation
        return jsonify({
            "success": False, 
            "error": "Split functionality not yet implemented"
        }), 501
        
    except Exception as e:
        current_app.logger.error(f"Error splitting copyright file: {e}")
        return jsonify({"success": False, "error": str(e)}), 500
