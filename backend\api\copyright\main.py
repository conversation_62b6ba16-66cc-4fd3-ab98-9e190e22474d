"""
Main copyright API module that brings together all the sub-modules
"""
from flask import Blueprint

# Import all the operation functions
from .core_operations import get_copyright_assets, add_copyright_asset, update_copyright_asset
from .cn_websites import get_cn_websites_file_image, move_cn_websites_files
from .bulk_operations import bulk_update_copyright_assets, bulk_update_copyrights_files
from .metadata import (
    get_copyright_types, get_copyright_methods, manage_copyright_types, 
    refresh_data_cache, get_plaintiff_registrations
)
from .image_processing import crop_copyright_asset, promote_copyright_asset, proxy_image, split_copyright_file

# Create the blueprint
copyright_api_bp = Blueprint('copyright_api_bp', __name__)

# --- Core CRUD Operations ---

@copyright_api_bp.route('/api/v1/copyright', methods=['GET'])
def get_copyright_assets_route():
    return get_copyright_assets()

@copyright_api_bp.route('/api/v1/copyright', methods=['POST'])
def add_copyright_asset_route():
    return add_copyright_asset()

@copyright_api_bp.route('/api/v1/copyright/<id>', methods=['PATCH'])
def update_copyright_asset_route(id):
    return update_copyright_asset(id)

# --- Bulk Operations ---

@copyright_api_bp.route('/api/v1/copyright/bulk', methods=['PATCH'])
def bulk_update_copyright_assets_route():
    return bulk_update_copyright_assets()

@copyright_api_bp.route('/api/v1/copyright/copyrights-files/bulk', methods=['POST'])
def bulk_update_copyrights_files_route():
    return bulk_update_copyrights_files()

# --- Image Processing ---

@copyright_api_bp.route('/api/v1/copyright/<int:id>/crop', methods=['POST'])
def crop_copyright_asset_route(id):
    return crop_copyright_asset(id)

@copyright_api_bp.route('/api/v1/copyright/<int:id>/promote', methods=['POST'])
def promote_copyright_asset_route(id):
    return promote_copyright_asset(id)

@copyright_api_bp.route('/api/v1/copyright/image-proxy')
def proxy_image_route():
    return proxy_image()

@copyright_api_bp.route('/api/v1/copyright/copyrights-files/<file_id>/split', methods=['POST'])
def split_copyright_file_route(file_id):
    return split_copyright_file(file_id)

# --- CN Websites Operations ---

@copyright_api_bp.route('/api/v1/copyright/cn-websites-files/<int:file_id>/image', methods=['GET'])
def get_cn_websites_file_image_route(file_id):
    return get_cn_websites_file_image(file_id)

@copyright_api_bp.route('/api/v1/copyright/cn-websites-files/move', methods=['POST'])
def move_cn_websites_files_route():
    return move_cn_websites_files()

# --- Metadata and Cache ---

@copyright_api_bp.route('/api/v1/copyright/types', methods=['GET'])
def get_copyright_types_route():
    return get_copyright_types()

@copyright_api_bp.route('/api/v1/copyright/methods', methods=['GET'])
def get_copyright_methods_route():
    return get_copyright_methods()

@copyright_api_bp.route('/api/v1/copyright/types', methods=['POST'])
def manage_copyright_types_route():
    return manage_copyright_types()

@copyright_api_bp.route('/api/v1/copyright/cache/refresh', methods=['POST'])
def refresh_data_cache_route():
    return refresh_data_cache()

@copyright_api_bp.route('/api/v1/copyright/plaintiffs/<int:plaintiff_id>/registrations', methods=['GET'])
def get_plaintiff_registrations_route(plaintiff_id):
    return get_plaintiff_registrations(plaintiff_id)
