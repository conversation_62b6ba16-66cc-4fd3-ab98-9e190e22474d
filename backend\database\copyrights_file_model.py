from sqlalchemy import (
    Column, String, Text, Boolean, TIMESTAMP, Integer, Date
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID, JSONB, ARRAY, BIGINT
from backend.extensions import db

class CopyrightsFiles(db.Model):
    __tablename__ = 'copyrights_files'
    __bind_key__ = 'maidalv_db'

    id = Column(PG_UUID(as_uuid=True), nullable=False, primary_key=True)
    filename = Column(Text)
    registration_number = Column(Text)
    method = Column(Text)
    production = Column(Boolean)
    type = Column(String)
    create_time = Column(TIMESTAMP, nullable=False)
    update_time = Column(TIMESTAMP, nullable=False)

    query = db.session.query_property()

    def __repr__(self):
        return f'<CopyrightsFiles {self.id}>'
