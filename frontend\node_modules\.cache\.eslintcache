[{"D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\index.js": "1", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\App.js": "2", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\reportWebVitals.js": "3", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\Layout.js": "4", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api.js": "5", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\PatentPlatformPage.js": "6", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\patent-viz\\PatentExplorerPage.js": "7", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\patent-viz\\PatentDashboardPage.js": "8", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\navigation\\PlatformSwitcher.js": "9", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\FilterControls.js": "10", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PatentTable.js": "11", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\QuickStatsDisplay.js": "12", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PaginationControls.js": "13", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PatentDetailModal.js": "14", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PatentImageModal.js": "15", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\StatisticsSection.js": "16", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\SingleStatisticDisplay.js": "17", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\MissingDataDisplay.js": "18", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PieChartDisplay.js": "19", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\TrademarkPage.js": "20", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\PatentPage.js": "21", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\CopyrightPage.js": "22", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\SettingsPage.js": "23", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\DashboardPage.js": "24", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ImageBrowser.js": "25", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ByProductView.js": "26", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\FeatureComputation.js": "27", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ByModelView.js": "28", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ImageUpload.js": "29", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\MetricsDisplay.js": "30", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ModelManagement.js": "31", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\CombinedScoresConfig.js": "32", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\CollectionManagement.js": "33", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\boundingbox\\ResultsPage.js": "34", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\boundingbox\\RankPage.js": "35", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\boundingbox\\PictureManagementPage.js": "36", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\boundingbox\\ModelPage.js": "37", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api_model_workbench.js": "38", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api_patent_viz.js": "39", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api_bounding_box.js": "40", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\trademark-viz\\ImageSearcherPage.js": "41", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\patent-viz\\ImageSearcherPage.js": "42", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\trademark-viz\\FullScreenImageModal.js": "43", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api_image_search.js": "44", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\FullScreenImageModal.js": "45", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\TrademarkPlatformPage.js": "46", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\trademark-viz\\TrademarkExplorerPage.js": "47", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api_trademark_viz.js": "48", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\trademark-viz\\TrademarkTable.js": "49", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\trademark-viz\\TrademarkDetailsModal.js": "50", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\trademark-viz\\TrademarkImageModal.js": "51", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\trademark-viz\\FilterControls.js": "52", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api_copyright_viz.js": "53", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\copyright-viz\\OrphanFinder.js": "54", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\copyright-viz\\Gallery.js": "55", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\copyright-viz\\Settings.js": "56", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\copyright-viz\\DuplicateReviewer.js": "57", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\copyright-viz\\AddImage.js": "58", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\copyright-viz\\SetTypeModal.js": "59", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\copyright-viz\\ImageDrawer.js": "60", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\copyright-viz\\Pagination.js": "61", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\DroppableZone.js": "62", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\DragDropContext.js": "63", "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\DraggableImageCard.js": "64"}, {"size": 535, "mtime": 1746189119581, "results": "65", "hashOfConfig": "66"}, {"size": 3507, "mtime": 1755251213294, "results": "67", "hashOfConfig": "66"}, {"size": 362, "mtime": 1746189119762, "results": "68", "hashOfConfig": "66"}, {"size": 4987, "mtime": 1755090140904, "results": "69", "hashOfConfig": "66"}, {"size": 2212, "mtime": 1752571686278, "results": "70", "hashOfConfig": "66"}, {"size": 1674, "mtime": 1754105133452, "results": "71", "hashOfConfig": "66"}, {"size": 10996, "mtime": 1755090140916, "results": "72", "hashOfConfig": "66"}, {"size": 4077, "mtime": 1749669447721, "results": "73", "hashOfConfig": "66"}, {"size": 3220, "mtime": 1754111407940, "results": "74", "hashOfConfig": "66"}, {"size": 11466, "mtime": 1755090140907, "results": "75", "hashOfConfig": "66"}, {"size": 5071, "mtime": 1747117955797, "results": "76", "hashOfConfig": "66"}, {"size": 1209, "mtime": 1747117942276, "results": "77", "hashOfConfig": "66"}, {"size": 1499, "mtime": 1747117964199, "results": "78", "hashOfConfig": "66"}, {"size": 7088, "mtime": 1747117979725, "results": "79", "hashOfConfig": "66"}, {"size": 3725, "mtime": 1747117991227, "results": "80", "hashOfConfig": "66"}, {"size": 3647, "mtime": 1747930472886, "results": "81", "hashOfConfig": "66"}, {"size": 957, "mtime": 1747925526951, "results": "82", "hashOfConfig": "66"}, {"size": 1801, "mtime": 1747117784720, "results": "83", "hashOfConfig": "66"}, {"size": 3711, "mtime": 1747930450370, "results": "84", "hashOfConfig": "66"}, {"size": 2265, "mtime": 1747120927926, "results": "85", "hashOfConfig": "66"}, {"size": 2108, "mtime": 1747120944082, "results": "86", "hashOfConfig": "66"}, {"size": 2144, "mtime": 1747120959457, "results": "87", "hashOfConfig": "66"}, {"size": 2894, "mtime": 1754115979269, "results": "88", "hashOfConfig": "66"}, {"size": 2192, "mtime": 1747120951693, "results": "89", "hashOfConfig": "66"}, {"size": 20241, "mtime": 1749669447712, "results": "90", "hashOfConfig": "66"}, {"size": 23251, "mtime": 1749669447710, "results": "91", "hashOfConfig": "66"}, {"size": 10106, "mtime": 1749669447712, "results": "92", "hashOfConfig": "66"}, {"size": 30255, "mtime": 1749669447709, "results": "93", "hashOfConfig": "66"}, {"size": 8742, "mtime": 1749669447713, "results": "94", "hashOfConfig": "66"}, {"size": 16824, "mtime": 1749669447715, "results": "95", "hashOfConfig": "66"}, {"size": 5769, "mtime": 1749669447716, "results": "96", "hashOfConfig": "66"}, {"size": 18740, "mtime": 1749669447711, "results": "97", "hashOfConfig": "66"}, {"size": 6213, "mtime": 1749669447710, "results": "98", "hashOfConfig": "66"}, {"size": 37513, "mtime": 1752571686276, "results": "99", "hashOfConfig": "66"}, {"size": 7207, "mtime": 1752571686275, "results": "100", "hashOfConfig": "66"}, {"size": 14384, "mtime": 1752571686273, "results": "101", "hashOfConfig": "66"}, {"size": 8525, "mtime": 1752571686272, "results": "102", "hashOfConfig": "66"}, {"size": 4596, "mtime": 1752571686280, "results": "103", "hashOfConfig": "66"}, {"size": 1401, "mtime": 1752571686281, "results": "104", "hashOfConfig": "66"}, {"size": 5530, "mtime": 1752571686279, "results": "105", "hashOfConfig": "66"}, {"size": 9966, "mtime": 1755090140917, "results": "106", "hashOfConfig": "66"}, {"size": 10871, "mtime": 1754105133454, "results": "107", "hashOfConfig": "66"}, {"size": 1331, "mtime": 1754105133451, "results": "108", "hashOfConfig": "66"}, {"size": 719, "mtime": 1755185375191, "results": "109", "hashOfConfig": "66"}, {"size": 1331, "mtime": 1754105133450, "results": "110", "hashOfConfig": "66"}, {"size": 1700, "mtime": 1755090140913, "results": "111", "hashOfConfig": "66"}, {"size": 13907, "mtime": 1755090140918, "results": "112", "hashOfConfig": "66"}, {"size": 1845, "mtime": 1755185405057, "results": "113", "hashOfConfig": "66"}, {"size": 5919, "mtime": 1755090140912, "results": "114", "hashOfConfig": "66"}, {"size": 6662, "mtime": 1755090140910, "results": "115", "hashOfConfig": "66"}, {"size": 1792, "mtime": 1755090140911, "results": "116", "hashOfConfig": "66"}, {"size": 12327, "mtime": 1755090140908, "results": "117", "hashOfConfig": "66"}, {"size": 5505, "mtime": 1755250547448, "results": "118", "hashOfConfig": "66"}, {"size": 5872, "mtime": 1755259522702, "results": "119", "hashOfConfig": "66"}, {"size": 23522, "mtime": 1755266203868, "results": "120", "hashOfConfig": "66"}, {"size": 2999, "mtime": 1755259547655, "results": "121", "hashOfConfig": "66"}, {"size": 4946, "mtime": 1755259534991, "results": "122", "hashOfConfig": "66"}, {"size": 8715, "mtime": 1755249483724, "results": "123", "hashOfConfig": "66"}, {"size": 1884, "mtime": 1754138516138, "results": "124", "hashOfConfig": "66"}, {"size": 27525, "mtime": 1755259646527, "results": "125", "hashOfConfig": "66"}, {"size": 1007, "mtime": 1754138721910, "results": "126", "hashOfConfig": "66"}, {"size": 823, "mtime": 1755254311446, "results": "127", "hashOfConfig": "66"}, {"size": 1073, "mtime": 1755254284010, "results": "128", "hashOfConfig": "66"}, {"size": 2379, "mtime": 1755254303338, "results": "129", "hashOfConfig": "66"}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "vhjvsf", {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\index.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\App.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\reportWebVitals.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\Layout.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\PatentPlatformPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\patent-viz\\PatentExplorerPage.js", ["322"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\patent-viz\\PatentDashboardPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\navigation\\PlatformSwitcher.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\FilterControls.js", ["323", "324"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PatentTable.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\QuickStatsDisplay.js", ["325"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PaginationControls.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PatentDetailModal.js", ["326"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PatentImageModal.js", ["327"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\StatisticsSection.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\SingleStatisticDisplay.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\MissingDataDisplay.js", ["328"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\PieChartDisplay.js", ["329", "330"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\TrademarkPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\PatentPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\CopyrightPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\SettingsPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\model-test-workbench\\DashboardPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ImageBrowser.js", [], ["331"], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ByProductView.js", ["332", "333", "334"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\FeatureComputation.js", ["335", "336"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ByModelView.js", ["337", "338"], ["339"], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ImageUpload.js", ["340"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\MetricsDisplay.js", ["341"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\ModelManagement.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\CombinedScoresConfig.js", ["342", "343", "344"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\model-test-workbench\\CollectionManagement.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\boundingbox\\ResultsPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\boundingbox\\RankPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\boundingbox\\PictureManagementPage.js", ["345", "346", "347", "348"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\boundingbox\\ModelPage.js", ["349"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api_model_workbench.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api_patent_viz.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api_bounding_box.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\trademark-viz\\ImageSearcherPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\patent-viz\\ImageSearcherPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\trademark-viz\\FullScreenImageModal.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api_image_search.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\patent-viz\\FullScreenImageModal.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\TrademarkPlatformPage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\trademark-viz\\TrademarkExplorerPage.js", ["350", "351", "352", "353", "354", "355", "356"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api_trademark_viz.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\trademark-viz\\TrademarkTable.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\trademark-viz\\TrademarkDetailsModal.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\trademark-viz\\TrademarkImageModal.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\trademark-viz\\FilterControls.js", ["357"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\services\\api_copyright_viz.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\copyright-viz\\OrphanFinder.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\copyright-viz\\Gallery.js", ["358", "359", "360"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\copyright-viz\\Settings.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\copyright-viz\\DuplicateReviewer.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\copyright-viz\\AddImage.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\copyright-viz\\SetTypeModal.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\copyright-viz\\ImageDrawer.js", ["361", "362", "363", "364", "365", "366", "367"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\pages\\copyright-viz\\Pagination.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\DroppableZone.js", [], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\DragDropContext.js", ["368"], [], "D:\\Documents\\Programing\\TRO\\ModelTestsWorkbench\\frontend\\src\\components\\DraggableImageCard.js", [], [], {"ruleId": "369", "severity": 1, "message": "370", "line": 13, "column": 10, "nodeType": "371", "messageId": "372", "endLine": 13, "endColumn": 19}, {"ruleId": "369", "severity": 1, "message": "373", "line": 13, "column": 3, "nodeType": "371", "messageId": "372", "endLine": 13, "endColumn": 6}, {"ruleId": "374", "severity": 1, "message": "375", "line": 41, "column": 33, "nodeType": "371", "endLine": 41, "endColumn": 44}, {"ruleId": "369", "severity": 1, "message": "373", "line": 2, "column": 10, "nodeType": "371", "messageId": "372", "endLine": 2, "endColumn": 13}, {"ruleId": "369", "severity": 1, "message": "376", "line": 17, "column": 3, "nodeType": "371", "messageId": "372", "endLine": 17, "endColumn": 10}, {"ruleId": "369", "severity": 1, "message": "377", "line": 13, "column": 3, "nodeType": "371", "messageId": "372", "endLine": 13, "endColumn": 13}, {"ruleId": "369", "severity": 1, "message": "373", "line": 2, "column": 71, "nodeType": "371", "messageId": "372", "endLine": 2, "endColumn": 74}, {"ruleId": "369", "severity": 1, "message": "378", "line": 2, "column": 46, "nodeType": "371", "messageId": "372", "endLine": 2, "endColumn": 53}, {"ruleId": "369", "severity": 1, "message": "379", "line": 45, "column": 5, "nodeType": "371", "messageId": "372", "endLine": 45, "endColumn": 16}, {"ruleId": "374", "severity": 1, "message": "375", "line": 180, "column": 41, "nodeType": "371", "endLine": 180, "endColumn": 52, "suppressions": "380"}, {"ruleId": "369", "severity": 1, "message": "381", "line": 23, "column": 8, "nodeType": "371", "messageId": "372", "endLine": 23, "endColumn": 18}, {"ruleId": "374", "severity": 1, "message": "382", "line": 162, "column": 8, "nodeType": "383", "endLine": 162, "endColumn": 81, "suggestions": "384"}, {"ruleId": "374", "severity": 1, "message": "385", "line": 183, "column": 8, "nodeType": "383", "endLine": 183, "endColumn": 52, "suggestions": "386"}, {"ruleId": "374", "severity": 1, "message": "387", "line": 59, "column": 6, "nodeType": "383", "endLine": 59, "endColumn": 8, "suggestions": "388"}, {"ruleId": "374", "severity": 1, "message": "389", "line": 168, "column": 34, "nodeType": "371", "endLine": 168, "endColumn": 41}, {"ruleId": "369", "severity": 1, "message": "390", "line": 1, "column": 51, "nodeType": "371", "messageId": "372", "endLine": 1, "endColumn": 57}, {"ruleId": "369", "severity": 1, "message": "381", "line": 24, "column": 8, "nodeType": "371", "messageId": "372", "endLine": 24, "endColumn": 18}, {"ruleId": "374", "severity": 1, "message": "391", "line": 172, "column": 8, "nodeType": "383", "endLine": 172, "endColumn": 64, "suggestions": "392", "suppressions": "393"}, {"ruleId": "369", "severity": 1, "message": "394", "line": 12, "column": 3, "nodeType": "371", "messageId": "372", "endLine": 12, "endColumn": 8}, {"ruleId": "374", "severity": 1, "message": "395", "line": 89, "column": 8, "nodeType": "383", "endLine": 89, "endColumn": 35, "suggestions": "396"}, {"ruleId": "369", "severity": 1, "message": "397", "line": 1, "column": 51, "nodeType": "371", "messageId": "372", "endLine": 1, "endColumn": 58}, {"ruleId": "374", "severity": 1, "message": "398", "line": 62, "column": 6, "nodeType": "383", "endLine": 62, "endColumn": 8, "suggestions": "399"}, {"ruleId": "374", "severity": 1, "message": "398", "line": 73, "column": 6, "nodeType": "383", "endLine": 73, "endColumn": 8, "suggestions": "400"}, {"ruleId": "369", "severity": 1, "message": "401", "line": 4, "column": 3, "nodeType": "371", "messageId": "372", "endLine": 4, "endColumn": 7}, {"ruleId": "369", "severity": 1, "message": "402", "line": 4, "column": 9, "nodeType": "371", "messageId": "372", "endLine": 4, "endColumn": 17}, {"ruleId": "369", "severity": 1, "message": "403", "line": 4, "column": 19, "nodeType": "371", "messageId": "372", "endLine": 4, "endColumn": 31}, {"ruleId": "369", "severity": 1, "message": "404", "line": 7, "column": 15, "nodeType": "371", "messageId": "372", "endLine": 7, "endColumn": 19}, {"ruleId": "369", "severity": 1, "message": "405", "line": 27, "column": 8, "nodeType": "371", "messageId": "372", "endLine": 27, "endColumn": 16}, {"ruleId": "369", "severity": 1, "message": "406", "line": 9, "column": 5, "nodeType": "371", "messageId": "372", "endLine": 9, "endColumn": 29}, {"ruleId": "369", "severity": 1, "message": "407", "line": 10, "column": 5, "nodeType": "371", "messageId": "372", "endLine": 10, "endColumn": 21}, {"ruleId": "369", "severity": 1, "message": "408", "line": 11, "column": 5, "nodeType": "371", "messageId": "372", "endLine": 11, "endColumn": 24}, {"ruleId": "369", "severity": 1, "message": "370", "line": 13, "column": 10, "nodeType": "371", "messageId": "372", "endLine": 13, "endColumn": 19}, {"ruleId": "369", "severity": 1, "message": "409", "line": 21, "column": 8, "nodeType": "371", "messageId": "372", "endLine": 21, "endColumn": 24}, {"ruleId": "369", "severity": 1, "message": "410", "line": 108, "column": 12, "nodeType": "371", "messageId": "372", "endLine": 108, "endColumn": 27}, {"ruleId": "369", "severity": 1, "message": "411", "line": 109, "column": 12, "nodeType": "371", "messageId": "372", "endLine": 109, "endColumn": 23}, {"ruleId": "374", "severity": 1, "message": "375", "line": 28, "column": 35, "nodeType": "371", "endLine": 28, "endColumn": 46}, {"ruleId": "369", "severity": 1, "message": "390", "line": 1, "column": 51, "nodeType": "371", "messageId": "372", "endLine": 1, "endColumn": 57}, {"ruleId": "369", "severity": 1, "message": "412", "line": 231, "column": 19, "nodeType": "371", "messageId": "372", "endLine": 231, "endColumn": 36}, {"ruleId": "369", "severity": 1, "message": "413", "line": 255, "column": 11, "nodeType": "371", "messageId": "372", "endLine": 255, "endColumn": 33}, {"ruleId": "414", "severity": 2, "message": "415", "line": 84, "column": 21, "nodeType": "371", "messageId": "416", "endLine": 84, "endColumn": 40}, {"ruleId": "414", "severity": 2, "message": "417", "line": 219, "column": 19, "nodeType": "371", "messageId": "416", "endLine": 219, "endColumn": 38}, {"ruleId": "414", "severity": 2, "message": "418", "line": 336, "column": 19, "nodeType": "371", "messageId": "416", "endLine": 336, "endColumn": 37}, {"ruleId": "369", "severity": 1, "message": "419", "line": 394, "column": 12, "nodeType": "371", "messageId": "372", "endLine": 394, "endColumn": 19}, {"ruleId": "414", "severity": 2, "message": "420", "line": 401, "column": 38, "nodeType": "371", "messageId": "416", "endLine": 401, "endColumn": 63}, {"ruleId": "369", "severity": 1, "message": "421", "line": 415, "column": 11, "nodeType": "371", "messageId": "372", "endLine": 415, "endColumn": 28}, {"ruleId": "369", "severity": 1, "message": "422", "line": 494, "column": 14, "nodeType": "371", "messageId": "372", "endLine": 494, "endColumn": 27}, {"ruleId": "369", "severity": 1, "message": "423", "line": 12, "column": 3, "nodeType": "371", "messageId": "372", "endLine": 12, "endColumn": 12}, "no-unused-vars", "'DayPicker' is defined but never used.", "Identifier", "unusedVar", "'Box' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "'Divider' is defined but never used.", "'Typography' is defined but never used.", "'Tooltip' is defined but never used.", "'hoverLabels' is assigned a value but never used.", ["424"], "'ZoomInIcon' is defined but never used.", "React Hook useEffect has unnecessary dependencies: 'DEFAULT_VISIBLE_SUGGESTIONS' and 'INITIAL_SUGGESTIONS_LIMIT'. Either exclude them or remove the dependency array. Outer scope values like 'INITIAL_SUGGESTIONS_LIMIT' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["425"], "React Hook useCallback has an unnecessary dependency: 'INITIAL_SUGGESTIONS_LIMIT'. Either exclude it or remove the dependency array. Outer scope values like 'INITIAL_SUGGESTIONS_LIMIT' aren't valid dependencies because mutating them doesn't re-render the component.", ["426"], "React Hook useCallback has a missing dependency: 'checkStatus'. Either include it or remove the dependency array.", ["427"], "The ref value 'intervalRefs.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'intervalRefs.current' to a variable inside the effect, and use that variable in the cleanup function.", "'useRef' is defined but never used.", "React Hook useEffect has a missing dependency: 'results'. Either include it or remove the dependency array.", ["428"], ["429"], "'Input' is defined but never used.", "React Hook useCallback has a missing dependency: 'loadingDetails'. Either include it or remove the dependency array.", ["430"], "'useMemo' is defined but never used.", "React Hook useCallback has a missing dependency: 'handleApiError'. Either include it or remove the dependency array.", ["431"], ["432"], "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'Grid' is defined but never used.", "'EditIcon' is defined but never used.", "'getPatentsForExploration' is defined but never used.", "'getPatentDetails' is defined but never used.", "'getPatentImagesInfo' is defined but never used.", "'PatentImageModal' is defined but never used.", "'isImagesLoading' is assigned a value but never used.", "'imagesError' is assigned a value but never used.", "'targetPlaintiffId' is assigned a value but never used.", "'handleCertificateClick' is assigned a value but never used.", "no-undef", "'getCopyrightMethods' is not defined.", "undef", "'moveCnWebsitesFiles' is not defined.", "'splitCopyrightFile' is not defined.", "'loading' is assigned a value but never used.", "'getPlaintiffRegistrations' is not defined.", "'handleAssignRegNo' is assigned a value but never used.", "'getCroppedImg' is defined but never used.", "'arrayMove' is defined but never used.", {"kind": "433", "justification": "434"}, {"desc": "435", "fix": "436"}, {"desc": "435", "fix": "437"}, {"desc": "438", "fix": "439"}, {"desc": "440", "fix": "441"}, {"kind": "433", "justification": "434"}, {"desc": "442", "fix": "443"}, {"desc": "444", "fix": "445"}, {"desc": "444", "fix": "446"}, "directive", "", "Update the dependencies array to be: [selectedProduct]", {"range": "447", "text": "448"}, {"range": "449", "text": "448"}, "Update the dependencies array to be: [checkStatus]", {"range": "450", "text": "451"}, "Update the dependencies array to be: [selectedModelId, ipCategory, currentPage, fetchResults, results]", {"range": "452", "text": "453"}, "Update the dependencies array to be: [selectedModel, loadingDetails, ipCategory]", {"range": "454", "text": "455"}, "Update the dependencies array to be: [handleApiError]", {"range": "456", "text": "457"}, {"range": "458", "text": "457"}, [8132, 8205], "[selectedProduct]", [9404, 9448], [1981, 1983], "[checkStatus]", [7580, 7636], "[selectedModelId, ipCategory, currentPage, fetchResults, results]", [4626, 4653], "[selectedModel, loadingDetails, ipCategory]", [3049, 3051], "[handleApiError]", [3410, 3412]]