{"ast": null, "code": "var _jsxFileName = \"D:\\\\Documents\\\\Programing\\\\TRO\\\\ModelTestsWorkbench\\\\frontend\\\\src\\\\pages\\\\copyright-viz\\\\Gallery.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { getCopyrightAssets, getCopyrightTypes, getCopyrightMethods, bulkUpdateCopyrightsFiles, moveCnWebsitesFiles } from '../../services/api_copyright_viz';\nimport Lightbox from \"yet-another-react-lightbox\";\nimport \"yet-another-react-lightbox/styles.css\";\nimport './Gallery.css';\nimport ImageDrawer from './ImageDrawer';\nimport SetTypeModal from './SetTypeModal';\nimport Pagination from './Pagination';\nimport DragDropContext from '../../components/DragDropContext';\nimport DraggableImageCard from '../../components/DraggableImageCard';\nimport DroppableZone from '../../components/DroppableZone';\n\n/**\r\n * Gallery component for browsing and managing copyright assets grouped by plaintiff.\r\n * It supports filtering, bulk actions, pagination, and detailed view of assets.\r\n */\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Gallery = () => {\n  _s();\n  // State for plaintiffs data, selections, and UI controls\n  const [plaintiffs, setPlaintiffs] = useState([]);\n  const [selectedAssets, setSelectedAssets] = useState(new Set());\n  const [currentSubsection, setCurrentSubsection] = useState('copyrights_files'); // Track which subsection is active for bulk actions\n  const [expandedPlaintiffs, setExpandedPlaintiffs] = useState(new Set());\n  const [filters, setFilters] = useState({\n    plaintiff_name: '',\n    registration_number: '',\n    method: '',\n    type: '',\n    production: '',\n    certificate_status: ''\n  });\n  const [pagination, setPagination] = useState({\n    page: 1,\n    per_page: 5,\n    total_pages: 1,\n    total_items: 0\n  }); // 5 plaintiffs per page\n  const [copyrightTypes, setCopyrightTypes] = useState([]);\n  const [methodOptions, setMethodOptions] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n\n  // Utility to safely coerce potentially NaN/undefined/null to displayable string\n  const safeText = (val, fallback = 'N/A') => {\n    if (val === null || val === undefined) return fallback;\n    if (typeof val === 'number' && Number.isNaN(val)) return fallback;\n    if (typeof val === 'string' && val.trim().toLowerCase() === 'nan') return fallback;\n    return String(val);\n  };\n\n  // State for modals and drawers\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxSlides, setLightboxSlides] = useState([]);\n  const [drawerAsset, setDrawerAsset] = useState(null);\n  const [drawerPlaintiffId, setDrawerPlaintiffId] = useState(null);\n  const [isSetTypeModalOpen, setIsSetTypeModalOpen] = useState(false);\n\n  // Drag and drop state\n  const [activeId, setActiveId] = useState(null);\n\n  /**\r\n   * Fetches plaintiffs data from the API based on current filters and pagination.\r\n   */\n  const fetchPlaintiffs = useCallback(async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      var _result$data$plaintif, _result$data;\n      // Construct query parameters from active filters\n      const activeFilters = Object.entries(filters).reduce((acc, [key, value]) => {\n        if (typeof value === 'string' && value !== '') {\n          acc[key] = value;\n        }\n        return acc;\n      }, {});\n      const params = {\n        ...activeFilters,\n        page: pagination.page,\n        per_page: pagination.per_page\n      };\n      const result = await getCopyrightAssets(params);\n      const incomingPlaintiffs = (_result$data$plaintif = result === null || result === void 0 ? void 0 : (_result$data = result.data) === null || _result$data === void 0 ? void 0 : _result$data.plaintiffs) !== null && _result$data$plaintif !== void 0 ? _result$data$plaintif : [];\n      setPlaintiffs(incomingPlaintiffs);\n      setPagination(result.data.pagination);\n    } catch (err) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  }, [filters, pagination.page, pagination.per_page]);\n\n  /**\r\n   * Initial data fetch for copyright types and plaintiffs.\r\n   */\n  useEffect(() => {\n    const fetchFilterOptions = async () => {\n      try {\n        const [typesResult, methodsResult] = await Promise.all([getCopyrightTypes(), getCopyrightMethods()]);\n        setCopyrightTypes(typesResult.data || []);\n        setMethodOptions(methodsResult.data || []);\n      } catch (err) {\n        console.error(\"Failed to fetch initial filters\", err);\n      }\n    };\n    fetchFilterOptions();\n  }, []);\n  useEffect(() => {\n    fetchPlaintiffs();\n  }, [fetchPlaintiffs]);\n\n  /**\r\n   * Handles changes in filter inputs.\r\n   */\n  const handleFilterChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFilters(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  /**\r\n   * Toggles the selection of an asset.\r\n   */\n  const handleSelectAsset = (assetId, subsection) => {\n    if (!assetId) return;\n    const newSelectedAssets = new Set(selectedAssets);\n    if (newSelectedAssets.has(assetId)) {\n      newSelectedAssets.delete(assetId);\n    } else {\n      newSelectedAssets.add(assetId);\n    }\n    setSelectedAssets(newSelectedAssets);\n    setCurrentSubsection(subsection); // Track which subsection the selection is from\n  };\n\n  /**\r\n   * Toggles the expansion state of a plaintiff section.\r\n   */\n  const togglePlaintiffExpansion = plaintiffId => {\n    const newExpanded = new Set(expandedPlaintiffs);\n    if (newExpanded.has(plaintiffId)) {\n      newExpanded.delete(plaintiffId);\n    } else {\n      newExpanded.add(plaintiffId);\n    }\n    setExpandedPlaintiffs(newExpanded);\n  };\n\n  /**\r\n   * Executes a bulk action on the selected assets.\r\n   */\n  const handleBulkAction = async (action, value = null) => {\n    if (selectedAssets.size === 0) return;\n    setLoading(true);\n    setError(null);\n    try {\n      const selectedIds = Array.from(selectedAssets);\n      if (currentSubsection === 'copyrights_files') {\n        // Handle copyrights_files bulk actions\n        if (action === 'set_production_true') {\n          await bulkUpdateCopyrightsFiles('set_production', selectedIds, true);\n        } else if (action === 'set_production_false') {\n          await bulkUpdateCopyrightsFiles('set_production', selectedIds, false);\n        } else if (action === 'set_type') {\n          await bulkUpdateCopyrightsFiles('set_type', selectedIds, value);\n        }\n      } else if (currentSubsection === 'cn_websites_files') {\n        // Handle cn_websites_files bulk actions\n        if (action === 'move_to_copyrights') {\n          await moveCnWebsitesFiles(selectedIds);\n        }\n      }\n      setSelectedAssets(new Set());\n      fetchPlaintiffs();\n    } catch (err) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  /**\r\n   * Opens the lightbox with the specified slides.\r\n   */\n  const openLightbox = slides => {\n    setLightboxSlides(slides);\n    setLightboxOpen(true);\n  };\n\n  /**\r\n   * Handles clicks on the main image of an asset card.\r\n   */\n  const handleImageClick = (e, asset) => {\n    e.stopPropagation();\n    if (asset.high_res_path) {\n      openLightbox([{\n        src: asset.high_res_path,\n        title: safeText(asset.registration_number, 'N/A')\n      }]);\n    }\n  };\n\n  /**\r\n   * Handles clicks on an asset card to open the details drawer.\r\n   */\n  const handleCardClick = (asset, plaintiffId) => {\n    if (asset.id) {\n      setDrawerAsset(asset);\n      setDrawerPlaintiffId(plaintiffId);\n    }\n  };\n\n  /**\r\n   * Handles drag start event\r\n   */\n  const handleDragStart = event => {\n    setActiveId(event.active.id);\n  };\n\n  /**\r\n   * Handles drag end event - moves CN websites files to copyrights files\r\n   */\n  const handleDragEnd = async event => {\n    const {\n      active,\n      over\n    } = event;\n    setActiveId(null);\n    if (!over) return;\n\n    // Check if we're dropping a CN websites file onto a copyrights files zone\n    if (active.id && over.id && over.id.startsWith('copyrights-files-')) {\n      const draggedFileId = active.id;\n      const targetPlaintiffId = over.id.replace('copyrights-files-', '');\n      try {\n        setLoading(true);\n        await moveCnWebsitesFiles([draggedFileId]);\n        await fetchPlaintiffs(); // Refresh data\n      } catch (err) {\n        setError(err.message);\n      } finally {\n        setLoading(false);\n      }\n    }\n  };\n\n  /**\r\n   * Handles page changes from the pagination component.\r\n   */\n  const handlePageChange = newPage => {\n    setPagination(prev => ({\n      ...prev,\n      page: newPage\n    }));\n  };\n\n  /**\r\n   * Handles clicks on the \"View Certificate\" link.\r\n   */\n  const handleCertificateClick = (e, asset) => {\n    e.stopPropagation();\n    if (asset.certificate_path) {\n      openLightbox([{\n        src: asset.certificate_path,\n        title: `Certificate for ${safeText(asset.registration_number, 'N/A')}`\n      }]);\n    }\n  };\n  if (error) return /*#__PURE__*/_jsxDEV(\"p\", {\n    style: {\n      color: 'red'\n    },\n    children: error\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 258,\n    columnNumber: 23\n  }, this);\n\n  // Get all draggable items for DndContext\n  const getAllDraggableItems = () => {\n    const items = [];\n    plaintiffs.forEach(plaintiffData => {\n      plaintiffData.cn_websites_files.forEach(file => {\n        items.push(file.id);\n      });\n    });\n    return items;\n  };\n  return /*#__PURE__*/_jsxDEV(DragDropContext, {\n    onDragStart: handleDragStart,\n    onDragEnd: handleDragEnd,\n    activeId: activeId,\n    items: getAllDraggableItems(),\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"gallery-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"filter-panel\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Copyright Gallery v2 - Filters\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 280,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"plaintiff_name\",\n              children: \"Plaintiff Name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"plaintiff_name\",\n              id: \"plaintiff_name\",\n              value: filters.plaintiff_name,\n              onChange: handleFilterChange,\n              placeholder: \"Fuzzy match, case-insensitive\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 282,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"registration_number\",\n              children: \"Registration Number\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 295,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              name: \"registration_number\",\n              id: \"registration_number\",\n              value: filters.registration_number,\n              onChange: handleFilterChange,\n              placeholder: \"e.g., VA 123456\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 296,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"method\",\n              children: \"Method\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 307,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"method\",\n              id: \"method\",\n              value: filters.method,\n              onChange: handleFilterChange,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"All\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 33\n              }, this), methodOptions.map(m => /*#__PURE__*/_jsxDEV(\"option\", {\n                value: m.name,\n                children: m.name\n              }, m.name, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 37\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 306,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"production\",\n              children: \"Production Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"production\",\n              id: \"production\",\n              value: filters.production,\n              onChange: handleFilterChange,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"All\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 324,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"true\",\n                children: \"Production\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 325,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"false\",\n                children: \"Non-Production\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"filter-group\",\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"certificate_status\",\n              children: \"Certificate Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 331,\n              columnNumber: 29\n            }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n              name: \"certificate_status\",\n              id: \"certificate_status\",\n              value: filters.certificate_status,\n              onChange: handleFilterChange,\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"\",\n                children: \"All\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"fetched\",\n                children: \"Fetched\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"not_fetched\",\n                children: \"Not Fetched\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 340,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"error\",\n                children: \"Error\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 341,\n                columnNumber: 33\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"pending\",\n                children: \"Pending\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 342,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 332,\n              columnNumber: 29\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 330,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 281,\n          columnNumber: 21\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"filter-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: fetchPlaintiffs,\n            disabled: loading,\n            children: loading ? 'Loading...' : 'Apply Filters'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 25\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => {\n              setFilters({\n                plaintiff_name: '',\n                registration_number: '',\n                method: '',\n                type: '',\n                production: '',\n                certificate_status: ''\n              });\n            },\n            disabled: loading,\n            children: \"Clear Filters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 25\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 21\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 279,\n        columnNumber: 17\n      }, this), selectedAssets.size > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"action-bar\",\n        children: [currentSubsection === 'copyrights_files' && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleBulkAction('set_production_true'),\n            disabled: loading,\n            children: \"Set Production True\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 372,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleBulkAction('set_production_false'),\n            disabled: loading,\n            children: \"Set Production False\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 375,\n            columnNumber: 33\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setIsSetTypeModalOpen(true),\n            disabled: loading,\n            children: \"Set Type\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 33\n          }, this)]\n        }, void 0, true), currentSubsection === 'cn_websites_files' && /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => handleBulkAction('move_to_copyrights'),\n          disabled: loading,\n          children: \"Move to Copyrights Files\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 384,\n          columnNumber: 29\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 21\n      }, this), loading ? /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 28\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"plaintiffs-container\",\n          children: plaintiffs.map(plaintiffData => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"plaintiff-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plaintiff-header\",\n              onClick: () => togglePlaintiffExpansion(plaintiffData.plaintiff.id),\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                children: [plaintiffData.plaintiff.name, \" (ID: \", plaintiffData.plaintiff.id, \")\", /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"counts\",\n                  children: [\"- Copyrights: \", plaintiffData.plaintiff.counts.copyrights_files, \", CN Websites: \", plaintiffData.plaintiff.counts.cn_websites_files]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 45\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 400,\n                columnNumber: 41\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `expand-icon ${expandedPlaintiffs.has(plaintiffData.plaintiff.id) ? 'expanded' : ''}`,\n                children: \"\\u25BC\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 37\n            }, this), expandedPlaintiffs.has(plaintiffData.plaintiff.id) && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"plaintiff-content\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"subsection\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: [\"Copyrights Files (\", plaintiffData.copyrights_files.length, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(DroppableZone, {\n                  id: `copyrights-files-${plaintiffData.plaintiff.id}`,\n                  className: \"image-grid\",\n                  acceptsFrom: ['cn_websites_files'],\n                  children: plaintiffData.copyrights_files.map(file => /*#__PURE__*/_jsxDEV(DraggableImageCard, {\n                    id: file.id,\n                    file: file,\n                    isSelected: selectedAssets.has(file.id),\n                    onSelect: handleSelectAsset,\n                    onCardClick: handleCardClick,\n                    onImageClick: handleImageClick,\n                    subsection: \"copyrights_files\",\n                    plaintiffId: plaintiffData.plaintiff.id,\n                    safeText: safeText,\n                    isDragDisabled: true\n                  }, file.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 57\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 417,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 45\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"subsection\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: [\"CN Websites Files (\", plaintiffData.cn_websites_files.length, \")\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 49\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"image-grid\",\n                  children: plaintiffData.cn_websites_files.map(file => /*#__PURE__*/_jsxDEV(DraggableImageCard, {\n                    id: file.id,\n                    file: file,\n                    isSelected: selectedAssets.has(file.id),\n                    onSelect: handleSelectAsset,\n                    onCardClick: handleCardClick,\n                    onImageClick: handleImageClick,\n                    subsection: \"cn_websites_files\",\n                    plaintiffId: plaintiffData.plaintiff.id,\n                    safeText: safeText,\n                    isDragDisabled: false\n                  }, file.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 445,\n                    columnNumber: 57\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 49\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 45\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 413,\n              columnNumber: 41\n            }, this)]\n          }, plaintiffData.plaintiff.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 33\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 393,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(Pagination, {\n          currentPage: pagination.page,\n          totalPages: pagination.total_pages,\n          onPageChange: handlePageChange\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 466,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 278,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(Lightbox, {\n      open: lightboxOpen,\n      close: () => setLightboxOpen(false),\n      slides: lightboxSlides\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 475,\n      columnNumber: 13\n    }, this), drawerAsset && /*#__PURE__*/_jsxDEV(ImageDrawer, {\n      asset: drawerAsset,\n      plaintiffId: drawerPlaintiffId,\n      onClose: () => {\n        setDrawerAsset(null);\n        setDrawerPlaintiffId(null);\n      },\n      onUpdate: () => {\n        setDrawerAsset(null);\n        setDrawerPlaintiffId(null);\n        fetchPlaintiffs();\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 481,\n      columnNumber: 17\n    }, this), isSetTypeModalOpen && /*#__PURE__*/_jsxDEV(SetTypeModal, {\n      types: copyrightTypes,\n      onClose: () => setIsSetTypeModalOpen(false),\n      onConfirm: selectedType => {\n        handleBulkAction('set_type', selectedType);\n        setIsSetTypeModalOpen(false);\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 17\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 272,\n    columnNumber: 9\n  }, this);\n};\n_s(Gallery, \"w5LXf+GlDBobqx/fyIYE7F0JPv4=\");\n_c = Gallery;\nexport default Gallery;\nvar _c;\n$RefreshReg$(_c, \"Gallery\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "getCopyrightAssets", "getCopyrightTypes", "getCopyrightMethods", "bulkUpdateCopyrightsFiles", "moveCnWebsitesFiles", "Lightbox", "ImageDrawer", "SetTypeModal", "Pagination", "DragDropContext", "DraggableImageCard", "DroppableZone", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Gallery", "_s", "plaintiffs", "set<PERSON><PERSON>tiffs", "selectedAssets", "setSelectedAssets", "Set", "currentSubsection", "setCurrentSubsection", "expandedPlaintiffs", "setExpandedPlaintiffs", "filters", "setFilters", "plaintiff_name", "registration_number", "method", "type", "production", "certificate_status", "pagination", "setPagination", "page", "per_page", "total_pages", "total_items", "copyrightTypes", "setCopyrightTypes", "methodOptions", "setMethodOptions", "loading", "setLoading", "error", "setError", "safeText", "val", "fallback", "undefined", "Number", "isNaN", "trim", "toLowerCase", "String", "lightboxOpen", "setLightboxOpen", "lightboxSlides", "setLightboxSlides", "drawerAsset", "setDrawerAsset", "drawer<PERSON><PERSON>tiffId", "setDrawerPlaintiffId", "isSetTypeModalOpen", "setIsSetTypeModalOpen", "activeId", "setActiveId", "fetchPlaintiffs", "_result$data$plaintif", "_result$data", "activeFilters", "Object", "entries", "reduce", "acc", "key", "value", "params", "result", "incomingPlaintiffs", "data", "err", "message", "fetchFilterOptions", "typesResult", "methodsResult", "Promise", "all", "console", "handleFilterChange", "e", "name", "target", "prev", "handleSelectAsset", "assetId", "subsection", "newSelectedAssets", "has", "delete", "add", "togglePlaintiffExpansion", "plaintiffId", "newExpanded", "handleBulkAction", "action", "size", "selectedIds", "Array", "from", "openLightbox", "slides", "handleImageClick", "asset", "stopPropagation", "high_res_path", "src", "title", "handleCardClick", "id", "handleDragStart", "event", "active", "handleDragEnd", "over", "startsWith", "draggedFileId", "targetPlaintiffId", "replace", "handlePageChange", "newPage", "handleCertificateClick", "certificate_path", "style", "color", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "getAllDraggableItems", "items", "for<PERSON>ach", "plaintiffData", "cn_websites_files", "file", "push", "onDragStart", "onDragEnd", "className", "htmlFor", "onChange", "placeholder", "map", "m", "onClick", "disabled", "plaintiff", "counts", "copyrights_files", "length", "acceptsFrom", "isSelected", "onSelect", "onCardClick", "onImageClick", "isDragDisabled", "currentPage", "totalPages", "onPageChange", "open", "close", "onClose", "onUpdate", "types", "onConfirm", "selectedType", "_c", "$RefreshReg$"], "sources": ["D:/Documents/Programing/TRO/ModelTestsWorkbench/frontend/src/pages/copyright-viz/Gallery.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\r\nimport { getCopyrightAssets, getCopyrightTypes, getCopyrightMethods, bulkUpdateCopyrightsFiles, moveCnWebsitesFiles } from '../../services/api_copyright_viz';\r\nimport Lightbox from \"yet-another-react-lightbox\";\r\nimport \"yet-another-react-lightbox/styles.css\";\r\nimport './Gallery.css';\r\nimport ImageDrawer from './ImageDrawer';\r\nimport SetTypeModal from './SetTypeModal';\r\nimport Pagination from './Pagination';\r\nimport DragDropContext from '../../components/DragDropContext';\r\nimport DraggableImageCard from '../../components/DraggableImageCard';\r\nimport DroppableZone from '../../components/DroppableZone';\r\n\r\n/**\r\n * Gallery component for browsing and managing copyright assets grouped by plaintiff.\r\n * It supports filtering, bulk actions, pagination, and detailed view of assets.\r\n */\r\nconst Gallery = () => {\r\n    // State for plaintiffs data, selections, and UI controls\r\n    const [plaintiffs, setPlaintiffs] = useState([]);\r\n    const [selectedAssets, setSelectedAssets] = useState(new Set());\r\n    const [currentSubsection, setCurrentSubsection] = useState('copyrights_files'); // Track which subsection is active for bulk actions\r\n    const [expandedPlaintiffs, setExpandedPlaintiffs] = useState(new Set());\r\n    const [filters, setFilters] = useState({\r\n        plaintiff_name: '',\r\n        registration_number: '',\r\n        method: '',\r\n        type: '',\r\n        production: '',\r\n        certificate_status: ''\r\n    });\r\n    const [pagination, setPagination] = useState({ page: 1, per_page: 5, total_pages: 1, total_items: 0 }); // 5 plaintiffs per page\r\n    const [copyrightTypes, setCopyrightTypes] = useState([]);\r\n    const [methodOptions, setMethodOptions] = useState([]);\r\n    const [loading, setLoading] = useState(true);\r\n    const [error, setError] = useState(null);\r\n\r\n    // Utility to safely coerce potentially NaN/undefined/null to displayable string\r\n    const safeText = (val, fallback = 'N/A') => {\r\n        if (val === null || val === undefined) return fallback;\r\n        if (typeof val === 'number' && Number.isNaN(val)) return fallback;\r\n        if (typeof val === 'string' && val.trim().toLowerCase() === 'nan') return fallback;\r\n        return String(val);\r\n    };\r\n\r\n    // State for modals and drawers\r\n    const [lightboxOpen, setLightboxOpen] = useState(false);\r\n    const [lightboxSlides, setLightboxSlides] = useState([]);\r\n    const [drawerAsset, setDrawerAsset] = useState(null);\r\n    const [drawerPlaintiffId, setDrawerPlaintiffId] = useState(null);\r\n    const [isSetTypeModalOpen, setIsSetTypeModalOpen] = useState(false);\r\n\r\n    // Drag and drop state\r\n    const [activeId, setActiveId] = useState(null);\r\n\r\n    /**\r\n     * Fetches plaintiffs data from the API based on current filters and pagination.\r\n     */\r\n    const fetchPlaintiffs = useCallback(async () => {\r\n        setLoading(true);\r\n        setError(null);\r\n        try {\r\n            // Construct query parameters from active filters\r\n            const activeFilters = Object.entries(filters).reduce((acc, [key, value]) => {\r\n                if (typeof value === 'string' && value !== '') {\r\n                    acc[key] = value;\r\n                }\r\n                return acc;\r\n            }, {});\r\n            const params = { ...activeFilters, page: pagination.page, per_page: pagination.per_page };\r\n\r\n            const result = await getCopyrightAssets(params);\r\n            const incomingPlaintiffs = (result?.data?.plaintiffs) ?? [];\r\n            setPlaintiffs(incomingPlaintiffs);\r\n            setPagination(result.data.pagination);\r\n        } catch (err) {\r\n            setError(err.message);\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    }, [filters, pagination.page, pagination.per_page]);\r\n\r\n    /**\r\n     * Initial data fetch for copyright types and plaintiffs.\r\n     */\r\n    useEffect(() => {\r\n        const fetchFilterOptions = async () => {\r\n            try {\r\n                const [typesResult, methodsResult] = await Promise.all([\r\n                    getCopyrightTypes(),\r\n                    getCopyrightMethods()\r\n                ]);\r\n                setCopyrightTypes(typesResult.data || []);\r\n                setMethodOptions(methodsResult.data || []);\r\n            } catch (err) {\r\n                console.error(\"Failed to fetch initial filters\", err);\r\n            }\r\n        };\r\n        fetchFilterOptions();\r\n    }, []);\r\n\r\n    useEffect(() => {\r\n        fetchPlaintiffs();\r\n    }, [fetchPlaintiffs]);\r\n\r\n    /**\r\n     * Handles changes in filter inputs.\r\n     */\r\n    const handleFilterChange = (e) => {\r\n        const { name, value } = e.target;\r\n        setFilters(prev => ({\r\n            ...prev,\r\n            [name]: value,\r\n        }));\r\n    };\r\n\r\n    /**\r\n     * Toggles the selection of an asset.\r\n     */\r\n    const handleSelectAsset = (assetId, subsection) => {\r\n        if (!assetId) return;\r\n        const newSelectedAssets = new Set(selectedAssets);\r\n        if (newSelectedAssets.has(assetId)) {\r\n            newSelectedAssets.delete(assetId);\r\n        } else {\r\n            newSelectedAssets.add(assetId);\r\n        }\r\n        setSelectedAssets(newSelectedAssets);\r\n        setCurrentSubsection(subsection); // Track which subsection the selection is from\r\n    };\r\n\r\n    /**\r\n     * Toggles the expansion state of a plaintiff section.\r\n     */\r\n    const togglePlaintiffExpansion = (plaintiffId) => {\r\n        const newExpanded = new Set(expandedPlaintiffs);\r\n        if (newExpanded.has(plaintiffId)) {\r\n            newExpanded.delete(plaintiffId);\r\n        } else {\r\n            newExpanded.add(plaintiffId);\r\n        }\r\n        setExpandedPlaintiffs(newExpanded);\r\n    };\r\n\r\n    /**\r\n     * Executes a bulk action on the selected assets.\r\n     */\r\n    const handleBulkAction = async (action, value = null) => {\r\n        if (selectedAssets.size === 0) return;\r\n\r\n        setLoading(true);\r\n        setError(null);\r\n        try {\r\n            const selectedIds = Array.from(selectedAssets);\r\n\r\n            if (currentSubsection === 'copyrights_files') {\r\n                // Handle copyrights_files bulk actions\r\n                if (action === 'set_production_true') {\r\n                    await bulkUpdateCopyrightsFiles('set_production', selectedIds, true);\r\n                } else if (action === 'set_production_false') {\r\n                    await bulkUpdateCopyrightsFiles('set_production', selectedIds, false);\r\n                } else if (action === 'set_type') {\r\n                    await bulkUpdateCopyrightsFiles('set_type', selectedIds, value);\r\n                }\r\n            } else if (currentSubsection === 'cn_websites_files') {\r\n                // Handle cn_websites_files bulk actions\r\n                if (action === 'move_to_copyrights') {\r\n                    await moveCnWebsitesFiles(selectedIds);\r\n                }\r\n            }\r\n\r\n            setSelectedAssets(new Set());\r\n            fetchPlaintiffs();\r\n        } catch (err) {\r\n            setError(err.message);\r\n        } finally {\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    /**\r\n     * Opens the lightbox with the specified slides.\r\n     */\r\n    const openLightbox = (slides) => {\r\n        setLightboxSlides(slides);\r\n        setLightboxOpen(true);\r\n    };\r\n\r\n    /**\r\n     * Handles clicks on the main image of an asset card.\r\n     */\r\n    const handleImageClick = (e, asset) => {\r\n        e.stopPropagation();\r\n        if (asset.high_res_path) {\r\n            openLightbox([{ src: asset.high_res_path, title: safeText(asset.registration_number, 'N/A') }]);\r\n        }\r\n    };\r\n\r\n    /**\r\n     * Handles clicks on an asset card to open the details drawer.\r\n     */\r\n    const handleCardClick = (asset, plaintiffId) => {\r\n        if (asset.id) {\r\n            setDrawerAsset(asset);\r\n            setDrawerPlaintiffId(plaintiffId);\r\n        }\r\n    };\r\n\r\n    /**\r\n     * Handles drag start event\r\n     */\r\n    const handleDragStart = (event) => {\r\n        setActiveId(event.active.id);\r\n    };\r\n\r\n    /**\r\n     * Handles drag end event - moves CN websites files to copyrights files\r\n     */\r\n    const handleDragEnd = async (event) => {\r\n        const { active, over } = event;\r\n        setActiveId(null);\r\n\r\n        if (!over) return;\r\n\r\n        // Check if we're dropping a CN websites file onto a copyrights files zone\r\n        if (active.id && over.id && over.id.startsWith('copyrights-files-')) {\r\n            const draggedFileId = active.id;\r\n            const targetPlaintiffId = over.id.replace('copyrights-files-', '');\r\n\r\n            try {\r\n                setLoading(true);\r\n                await moveCnWebsitesFiles([draggedFileId]);\r\n                await fetchPlaintiffs(); // Refresh data\r\n            } catch (err) {\r\n                setError(err.message);\r\n            } finally {\r\n                setLoading(false);\r\n            }\r\n        }\r\n    };\r\n\r\n    /**\r\n     * Handles page changes from the pagination component.\r\n     */\r\n    const handlePageChange = (newPage) => {\r\n        setPagination(prev => ({ ...prev, page: newPage }));\r\n    };\r\n\r\n    /**\r\n     * Handles clicks on the \"View Certificate\" link.\r\n     */\r\n    const handleCertificateClick = (e, asset) => {\r\n        e.stopPropagation();\r\n        if (asset.certificate_path) {\r\n            openLightbox([{ src: asset.certificate_path, title: `Certificate for ${safeText(asset.registration_number, 'N/A')}` }]);\r\n        }\r\n    };\r\n\r\n    if (error) return <p style={{ color: 'red' }}>{error}</p>;\r\n\r\n    // Get all draggable items for DndContext\r\n    const getAllDraggableItems = () => {\r\n        const items = [];\r\n        plaintiffs.forEach(plaintiffData => {\r\n            plaintiffData.cn_websites_files.forEach(file => {\r\n                items.push(file.id);\r\n            });\r\n        });\r\n        return items;\r\n    };\r\n\r\n    return (\r\n        <DragDropContext\r\n            onDragStart={handleDragStart}\r\n            onDragEnd={handleDragEnd}\r\n            activeId={activeId}\r\n            items={getAllDraggableItems()}\r\n        >\r\n            <div className=\"gallery-container\">\r\n                <div className=\"filter-panel\">\r\n                    <h2>Copyright Gallery v2 - Filters</h2>\r\n                    <div className=\"filter-grid\">\r\n                        <div className=\"filter-group\">\r\n                            <label htmlFor=\"plaintiff_name\">Plaintiff Name</label>\r\n                            <input\r\n                                type=\"text\"\r\n                                name=\"plaintiff_name\"\r\n                                id=\"plaintiff_name\"\r\n                                value={filters.plaintiff_name}\r\n                                onChange={handleFilterChange}\r\n                                placeholder=\"Fuzzy match, case-insensitive\"\r\n                            />\r\n                        </div>\r\n\r\n                        <div className=\"filter-group\">\r\n                            <label htmlFor=\"registration_number\">Registration Number</label>\r\n                            <input\r\n                                type=\"text\"\r\n                                name=\"registration_number\"\r\n                                id=\"registration_number\"\r\n                                value={filters.registration_number}\r\n                                onChange={handleFilterChange}\r\n                                placeholder=\"e.g., VA 123456\"\r\n                            />\r\n                        </div>\r\n\r\n                        <div className=\"filter-group\">\r\n                            <label htmlFor=\"method\">Method</label>\r\n                            <select name=\"method\" id=\"method\" value={filters.method} onChange={handleFilterChange}>\r\n                                <option value=\"\">All</option>\r\n                                {methodOptions.map((m) => (\r\n                                    <option key={m.name} value={m.name}>{m.name}</option>\r\n                                ))}\r\n                            </select>\r\n                        </div>\r\n\r\n                        <div className=\"filter-group\">\r\n                            <label htmlFor=\"production\">Production Status</label>\r\n                            <select\r\n                                name=\"production\"\r\n                                id=\"production\"\r\n                                value={filters.production}\r\n                                onChange={handleFilterChange}\r\n                            >\r\n                                <option value=\"\">All</option>\r\n                                <option value=\"true\">Production</option>\r\n                                <option value=\"false\">Non-Production</option>\r\n                            </select>\r\n                        </div>\r\n\r\n                        <div className=\"filter-group\">\r\n                            <label htmlFor=\"certificate_status\">Certificate Status</label>\r\n                            <select\r\n                                name=\"certificate_status\"\r\n                                id=\"certificate_status\"\r\n                                value={filters.certificate_status}\r\n                                onChange={handleFilterChange}\r\n                            >\r\n                                <option value=\"\">All</option>\r\n                                <option value=\"fetched\">Fetched</option>\r\n                                <option value=\"not_fetched\">Not Fetched</option>\r\n                                <option value=\"error\">Error</option>\r\n                                <option value=\"pending\">Pending</option>\r\n                            </select>\r\n                        </div>\r\n                    </div>\r\n                    <div className=\"filter-actions\">\r\n                        <button onClick={fetchPlaintiffs} disabled={loading}>\r\n                            {loading ? 'Loading...' : 'Apply Filters'}\r\n                        </button>\r\n                        <button\r\n                            onClick={() => {\r\n                                setFilters({\r\n                                    plaintiff_name: '',\r\n                                    registration_number: '',\r\n                                    method: '',\r\n                                    type: '',\r\n                                    production: '',\r\n                                    certificate_status: ''\r\n                                });\r\n                            }}\r\n                            disabled={loading}\r\n                        >\r\n                            Clear Filters\r\n                        </button>\r\n                    </div>\r\n                </div>\r\n                {/* Bulk Action Toolbar - contextual to current subsection */}\r\n                {selectedAssets.size > 0 && (\r\n                    <div className=\"action-bar\">\r\n                        {currentSubsection === 'copyrights_files' && (\r\n                            <>\r\n                                <button onClick={() => handleBulkAction('set_production_true')} disabled={loading}>\r\n                                    Set Production True\r\n                                </button>\r\n                                <button onClick={() => handleBulkAction('set_production_false')} disabled={loading}>\r\n                                    Set Production False\r\n                                </button>\r\n                                <button onClick={() => setIsSetTypeModalOpen(true)} disabled={loading}>\r\n                                    Set Type\r\n                                </button>\r\n                            </>\r\n                        )}\r\n                        {currentSubsection === 'cn_websites_files' && (\r\n                            <button onClick={() => handleBulkAction('move_to_copyrights')} disabled={loading}>\r\n                                Move to Copyrights Files\r\n                            </button>\r\n                        )}\r\n                    </div>\r\n                )}\r\n\r\n                {loading ? <p>Loading...</p> : (\r\n                    <>\r\n                        <div className=\"plaintiffs-container\">\r\n                            {plaintiffs.map(plaintiffData => (\r\n                                <div key={plaintiffData.plaintiff.id} className=\"plaintiff-section\">\r\n                                    <div\r\n                                        className=\"plaintiff-header\"\r\n                                        onClick={() => togglePlaintiffExpansion(plaintiffData.plaintiff.id)}\r\n                                    >\r\n                                        <h3>\r\n                                            {plaintiffData.plaintiff.name} (ID: {plaintiffData.plaintiff.id})\r\n                                            <span className=\"counts\">\r\n                                                - Copyrights: {plaintiffData.plaintiff.counts.copyrights_files},\r\n                                                CN Websites: {plaintiffData.plaintiff.counts.cn_websites_files}\r\n                                            </span>\r\n                                        </h3>\r\n                                        <span className={`expand-icon ${expandedPlaintiffs.has(plaintiffData.plaintiff.id) ? 'expanded' : ''}`}>\r\n                                            ▼\r\n                                        </span>\r\n                                    </div>\r\n\r\n                                    {expandedPlaintiffs.has(plaintiffData.plaintiff.id) && (\r\n                                        <div className=\"plaintiff-content\">\r\n                                            {/* Copyrights Files Subsection */}\r\n                                            <div className=\"subsection\">\r\n                                                <h4>Copyrights Files ({plaintiffData.copyrights_files.length})</h4>\r\n                                                <DroppableZone\r\n                                                    id={`copyrights-files-${plaintiffData.plaintiff.id}`}\r\n                                                    className=\"image-grid\"\r\n                                                    acceptsFrom={['cn_websites_files']}\r\n                                                >\r\n                                                    {plaintiffData.copyrights_files.map(file => (\r\n                                                        <DraggableImageCard\r\n                                                            key={file.id}\r\n                                                            id={file.id}\r\n                                                            file={file}\r\n                                                            isSelected={selectedAssets.has(file.id)}\r\n                                                            onSelect={handleSelectAsset}\r\n                                                            onCardClick={handleCardClick}\r\n                                                            onImageClick={handleImageClick}\r\n                                                            subsection=\"copyrights_files\"\r\n                                                            plaintiffId={plaintiffData.plaintiff.id}\r\n                                                            safeText={safeText}\r\n                                                            isDragDisabled={true}\r\n                                                        />\r\n                                                    ))}\r\n                                                </DroppableZone>\r\n                                            </div>\r\n\r\n                                            {/* CN Websites Files Subsection */}\r\n                                            <div className=\"subsection\">\r\n                                                <h4>CN Websites Files ({plaintiffData.cn_websites_files.length})</h4>\r\n                                                <div className=\"image-grid\">\r\n                                                    {plaintiffData.cn_websites_files.map(file => (\r\n                                                        <DraggableImageCard\r\n                                                            key={file.id}\r\n                                                            id={file.id}\r\n                                                            file={file}\r\n                                                            isSelected={selectedAssets.has(file.id)}\r\n                                                            onSelect={handleSelectAsset}\r\n                                                            onCardClick={handleCardClick}\r\n                                                            onImageClick={handleImageClick}\r\n                                                            subsection=\"cn_websites_files\"\r\n                                                            plaintiffId={plaintiffData.plaintiff.id}\r\n                                                            safeText={safeText}\r\n                                                            isDragDisabled={false}\r\n                                                        />\r\n                                                    ))}\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    )}\r\n                                </div>\r\n                            ))}\r\n                        </div>\r\n                        <Pagination\r\n                            currentPage={pagination.page}\r\n                            totalPages={pagination.total_pages}\r\n                            onPageChange={handlePageChange}\r\n                        />\r\n                    </>\r\n                )}\r\n            </div>\r\n            {/* Modals and Drawers */}\r\n            <Lightbox\r\n                open={lightboxOpen}\r\n                close={() => setLightboxOpen(false)}\r\n                slides={lightboxSlides}\r\n            />\r\n            {drawerAsset && (\r\n                <ImageDrawer\r\n                    asset={drawerAsset}\r\n                    plaintiffId={drawerPlaintiffId}\r\n                    onClose={() => {\r\n                        setDrawerAsset(null);\r\n                        setDrawerPlaintiffId(null);\r\n                    }}\r\n                    onUpdate={() => {\r\n                        setDrawerAsset(null);\r\n                        setDrawerPlaintiffId(null);\r\n                        fetchPlaintiffs();\r\n                    }}\r\n                />\r\n            )}\r\n            {isSetTypeModalOpen && (\r\n                <SetTypeModal\r\n                    types={copyrightTypes}\r\n                    onClose={() => setIsSetTypeModalOpen(false)}\r\n                    onConfirm={(selectedType) => {\r\n                        handleBulkAction('set_type', selectedType);\r\n                        setIsSetTypeModalOpen(false);\r\n                    }}\r\n                />\r\n            )}\r\n        </DragDropContext>\r\n    );\r\n};\r\n\r\nexport default Gallery;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,kBAAkB,EAAEC,iBAAiB,EAAEC,mBAAmB,EAAEC,yBAAyB,EAAEC,mBAAmB,QAAQ,kCAAkC;AAC7J,OAAOC,QAAQ,MAAM,4BAA4B;AACjD,OAAO,uCAAuC;AAC9C,OAAO,eAAe;AACtB,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,UAAU,MAAM,cAAc;AACrC,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,kBAAkB,MAAM,qCAAqC;AACpE,OAAOC,aAAa,MAAM,gCAAgC;;AAE1D;AACA;AACA;AACA;AAHA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAIA,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB;EACA,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,IAAIyB,GAAG,CAAC,CAAC,CAAC;EAC/D,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG3B,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC;EAChF,MAAM,CAAC4B,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG7B,QAAQ,CAAC,IAAIyB,GAAG,CAAC,CAAC,CAAC;EACvE,MAAM,CAACK,OAAO,EAAEC,UAAU,CAAC,GAAG/B,QAAQ,CAAC;IACnCgC,cAAc,EAAE,EAAE;IAClBC,mBAAmB,EAAE,EAAE;IACvBC,MAAM,EAAE,EAAE;IACVC,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,EAAE;IACdC,kBAAkB,EAAE;EACxB,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvC,QAAQ,CAAC;IAAEwC,IAAI,EAAE,CAAC;IAAEC,QAAQ,EAAE,CAAC;IAAEC,WAAW,EAAE,CAAC;IAAEC,WAAW,EAAE;EAAE,CAAC,CAAC,CAAC,CAAC;EACxG,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG7C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8C,aAAa,EAAEC,gBAAgB,CAAC,GAAG/C,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACgD,OAAO,EAAEC,UAAU,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACkD,KAAK,EAAEC,QAAQ,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;;EAExC;EACA,MAAMoD,QAAQ,GAAGA,CAACC,GAAG,EAAEC,QAAQ,GAAG,KAAK,KAAK;IACxC,IAAID,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAKE,SAAS,EAAE,OAAOD,QAAQ;IACtD,IAAI,OAAOD,GAAG,KAAK,QAAQ,IAAIG,MAAM,CAACC,KAAK,CAACJ,GAAG,CAAC,EAAE,OAAOC,QAAQ;IACjE,IAAI,OAAOD,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACK,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK,EAAE,OAAOL,QAAQ;IAClF,OAAOM,MAAM,CAACP,GAAG,CAAC;EACtB,CAAC;;EAED;EACA,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC+D,cAAc,EAAEC,iBAAiB,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACiE,WAAW,EAAEC,cAAc,CAAC,GAAGlE,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmE,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpE,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACqE,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtE,QAAQ,CAAC,KAAK,CAAC;;EAEnE;EACA,MAAM,CAACuE,QAAQ,EAAEC,WAAW,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC;;EAE9C;AACJ;AACA;EACI,MAAMyE,eAAe,GAAGvE,WAAW,CAAC,YAAY;IAC5C+C,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MAAA,IAAAuB,qBAAA,EAAAC,YAAA;MACA;MACA,MAAMC,aAAa,GAAGC,MAAM,CAACC,OAAO,CAAChD,OAAO,CAAC,CAACiD,MAAM,CAAC,CAACC,GAAG,EAAE,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAK;QACxE,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,EAAE,EAAE;UAC3CF,GAAG,CAACC,GAAG,CAAC,GAAGC,KAAK;QACpB;QACA,OAAOF,GAAG;MACd,CAAC,EAAE,CAAC,CAAC,CAAC;MACN,MAAMG,MAAM,GAAG;QAAE,GAAGP,aAAa;QAAEpC,IAAI,EAAEF,UAAU,CAACE,IAAI;QAAEC,QAAQ,EAAEH,UAAU,CAACG;MAAS,CAAC;MAEzF,MAAM2C,MAAM,GAAG,MAAMjF,kBAAkB,CAACgF,MAAM,CAAC;MAC/C,MAAME,kBAAkB,IAAAX,qBAAA,GAAIU,MAAM,aAANA,MAAM,wBAAAT,YAAA,GAANS,MAAM,CAAEE,IAAI,cAAAX,YAAA,uBAAZA,YAAA,CAActD,UAAU,cAAAqD,qBAAA,cAAAA,qBAAA,GAAK,EAAE;MAC3DpD,aAAa,CAAC+D,kBAAkB,CAAC;MACjC9C,aAAa,CAAC6C,MAAM,CAACE,IAAI,CAAChD,UAAU,CAAC;IACzC,CAAC,CAAC,OAAOiD,GAAG,EAAE;MACVpC,QAAQ,CAACoC,GAAG,CAACC,OAAO,CAAC;IACzB,CAAC,SAAS;MACNvC,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC,EAAE,CAACnB,OAAO,EAAEQ,UAAU,CAACE,IAAI,EAAEF,UAAU,CAACG,QAAQ,CAAC,CAAC;;EAEnD;AACJ;AACA;EACIxC,SAAS,CAAC,MAAM;IACZ,MAAMwF,kBAAkB,GAAG,MAAAA,CAAA,KAAY;MACnC,IAAI;QACA,MAAM,CAACC,WAAW,EAAEC,aAAa,CAAC,GAAG,MAAMC,OAAO,CAACC,GAAG,CAAC,CACnDzF,iBAAiB,CAAC,CAAC,EACnBC,mBAAmB,CAAC,CAAC,CACxB,CAAC;QACFwC,iBAAiB,CAAC6C,WAAW,CAACJ,IAAI,IAAI,EAAE,CAAC;QACzCvC,gBAAgB,CAAC4C,aAAa,CAACL,IAAI,IAAI,EAAE,CAAC;MAC9C,CAAC,CAAC,OAAOC,GAAG,EAAE;QACVO,OAAO,CAAC5C,KAAK,CAAC,iCAAiC,EAAEqC,GAAG,CAAC;MACzD;IACJ,CAAC;IACDE,kBAAkB,CAAC,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EAENxF,SAAS,CAAC,MAAM;IACZwE,eAAe,CAAC,CAAC;EACrB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;;EAErB;AACJ;AACA;EACI,MAAMsB,kBAAkB,GAAIC,CAAC,IAAK;IAC9B,MAAM;MAAEC,IAAI;MAAEf;IAAM,CAAC,GAAGc,CAAC,CAACE,MAAM;IAChCnE,UAAU,CAACoE,IAAI,KAAK;MAChB,GAAGA,IAAI;MACP,CAACF,IAAI,GAAGf;IACZ,CAAC,CAAC,CAAC;EACP,CAAC;;EAED;AACJ;AACA;EACI,MAAMkB,iBAAiB,GAAGA,CAACC,OAAO,EAAEC,UAAU,KAAK;IAC/C,IAAI,CAACD,OAAO,EAAE;IACd,MAAME,iBAAiB,GAAG,IAAI9E,GAAG,CAACF,cAAc,CAAC;IACjD,IAAIgF,iBAAiB,CAACC,GAAG,CAACH,OAAO,CAAC,EAAE;MAChCE,iBAAiB,CAACE,MAAM,CAACJ,OAAO,CAAC;IACrC,CAAC,MAAM;MACHE,iBAAiB,CAACG,GAAG,CAACL,OAAO,CAAC;IAClC;IACA7E,iBAAiB,CAAC+E,iBAAiB,CAAC;IACpC5E,oBAAoB,CAAC2E,UAAU,CAAC,CAAC,CAAC;EACtC,CAAC;;EAED;AACJ;AACA;EACI,MAAMK,wBAAwB,GAAIC,WAAW,IAAK;IAC9C,MAAMC,WAAW,GAAG,IAAIpF,GAAG,CAACG,kBAAkB,CAAC;IAC/C,IAAIiF,WAAW,CAACL,GAAG,CAACI,WAAW,CAAC,EAAE;MAC9BC,WAAW,CAACJ,MAAM,CAACG,WAAW,CAAC;IACnC,CAAC,MAAM;MACHC,WAAW,CAACH,GAAG,CAACE,WAAW,CAAC;IAChC;IACA/E,qBAAqB,CAACgF,WAAW,CAAC;EACtC,CAAC;;EAED;AACJ;AACA;EACI,MAAMC,gBAAgB,GAAG,MAAAA,CAAOC,MAAM,EAAE7B,KAAK,GAAG,IAAI,KAAK;IACrD,IAAI3D,cAAc,CAACyF,IAAI,KAAK,CAAC,EAAE;IAE/B/D,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,IAAI,CAAC;IACd,IAAI;MACA,MAAM8D,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC5F,cAAc,CAAC;MAE9C,IAAIG,iBAAiB,KAAK,kBAAkB,EAAE;QAC1C;QACA,IAAIqF,MAAM,KAAK,qBAAqB,EAAE;UAClC,MAAMzG,yBAAyB,CAAC,gBAAgB,EAAE2G,WAAW,EAAE,IAAI,CAAC;QACxE,CAAC,MAAM,IAAIF,MAAM,KAAK,sBAAsB,EAAE;UAC1C,MAAMzG,yBAAyB,CAAC,gBAAgB,EAAE2G,WAAW,EAAE,KAAK,CAAC;QACzE,CAAC,MAAM,IAAIF,MAAM,KAAK,UAAU,EAAE;UAC9B,MAAMzG,yBAAyB,CAAC,UAAU,EAAE2G,WAAW,EAAE/B,KAAK,CAAC;QACnE;MACJ,CAAC,MAAM,IAAIxD,iBAAiB,KAAK,mBAAmB,EAAE;QAClD;QACA,IAAIqF,MAAM,KAAK,oBAAoB,EAAE;UACjC,MAAMxG,mBAAmB,CAAC0G,WAAW,CAAC;QAC1C;MACJ;MAEAzF,iBAAiB,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;MAC5BgD,eAAe,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOc,GAAG,EAAE;MACVpC,QAAQ,CAACoC,GAAG,CAACC,OAAO,CAAC;IACzB,CAAC,SAAS;MACNvC,UAAU,CAAC,KAAK,CAAC;IACrB;EACJ,CAAC;;EAED;AACJ;AACA;EACI,MAAMmE,YAAY,GAAIC,MAAM,IAAK;IAC7BrD,iBAAiB,CAACqD,MAAM,CAAC;IACzBvD,eAAe,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;AACJ;AACA;EACI,MAAMwD,gBAAgB,GAAGA,CAACtB,CAAC,EAAEuB,KAAK,KAAK;IACnCvB,CAAC,CAACwB,eAAe,CAAC,CAAC;IACnB,IAAID,KAAK,CAACE,aAAa,EAAE;MACrBL,YAAY,CAAC,CAAC;QAAEM,GAAG,EAAEH,KAAK,CAACE,aAAa;QAAEE,KAAK,EAAEvE,QAAQ,CAACmE,KAAK,CAACtF,mBAAmB,EAAE,KAAK;MAAE,CAAC,CAAC,CAAC;IACnG;EACJ,CAAC;;EAED;AACJ;AACA;EACI,MAAM2F,eAAe,GAAGA,CAACL,KAAK,EAAEX,WAAW,KAAK;IAC5C,IAAIW,KAAK,CAACM,EAAE,EAAE;MACV3D,cAAc,CAACqD,KAAK,CAAC;MACrBnD,oBAAoB,CAACwC,WAAW,CAAC;IACrC;EACJ,CAAC;;EAED;AACJ;AACA;EACI,MAAMkB,eAAe,GAAIC,KAAK,IAAK;IAC/BvD,WAAW,CAACuD,KAAK,CAACC,MAAM,CAACH,EAAE,CAAC;EAChC,CAAC;;EAED;AACJ;AACA;EACI,MAAMI,aAAa,GAAG,MAAOF,KAAK,IAAK;IACnC,MAAM;MAAEC,MAAM;MAAEE;IAAK,CAAC,GAAGH,KAAK;IAC9BvD,WAAW,CAAC,IAAI,CAAC;IAEjB,IAAI,CAAC0D,IAAI,EAAE;;IAEX;IACA,IAAIF,MAAM,CAACH,EAAE,IAAIK,IAAI,CAACL,EAAE,IAAIK,IAAI,CAACL,EAAE,CAACM,UAAU,CAAC,mBAAmB,CAAC,EAAE;MACjE,MAAMC,aAAa,GAAGJ,MAAM,CAACH,EAAE;MAC/B,MAAMQ,iBAAiB,GAAGH,IAAI,CAACL,EAAE,CAACS,OAAO,CAAC,mBAAmB,EAAE,EAAE,CAAC;MAElE,IAAI;QACArF,UAAU,CAAC,IAAI,CAAC;QAChB,MAAM1C,mBAAmB,CAAC,CAAC6H,aAAa,CAAC,CAAC;QAC1C,MAAM3D,eAAe,CAAC,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,OAAOc,GAAG,EAAE;QACVpC,QAAQ,CAACoC,GAAG,CAACC,OAAO,CAAC;MACzB,CAAC,SAAS;QACNvC,UAAU,CAAC,KAAK,CAAC;MACrB;IACJ;EACJ,CAAC;;EAED;AACJ;AACA;EACI,MAAMsF,gBAAgB,GAAIC,OAAO,IAAK;IAClCjG,aAAa,CAAC4D,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE3D,IAAI,EAAEgG;IAAQ,CAAC,CAAC,CAAC;EACvD,CAAC;;EAED;AACJ;AACA;EACI,MAAMC,sBAAsB,GAAGA,CAACzC,CAAC,EAAEuB,KAAK,KAAK;IACzCvB,CAAC,CAACwB,eAAe,CAAC,CAAC;IACnB,IAAID,KAAK,CAACmB,gBAAgB,EAAE;MACxBtB,YAAY,CAAC,CAAC;QAAEM,GAAG,EAAEH,KAAK,CAACmB,gBAAgB;QAAEf,KAAK,EAAE,mBAAmBvE,QAAQ,CAACmE,KAAK,CAACtF,mBAAmB,EAAE,KAAK,CAAC;MAAG,CAAC,CAAC,CAAC;IAC3H;EACJ,CAAC;EAED,IAAIiB,KAAK,EAAE,oBAAOlC,OAAA;IAAG2H,KAAK,EAAE;MAAEC,KAAK,EAAE;IAAM,CAAE;IAAAC,QAAA,EAAE3F;EAAK;IAAA4F,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC;;EAEzD;EACA,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,KAAK,GAAG,EAAE;IAChB9H,UAAU,CAAC+H,OAAO,CAACC,aAAa,IAAI;MAChCA,aAAa,CAACC,iBAAiB,CAACF,OAAO,CAACG,IAAI,IAAI;QAC5CJ,KAAK,CAACK,IAAI,CAACD,IAAI,CAAC1B,EAAE,CAAC;MACvB,CAAC,CAAC;IACN,CAAC,CAAC;IACF,OAAOsB,KAAK;EAChB,CAAC;EAED,oBACInI,OAAA,CAACJ,eAAe;IACZ6I,WAAW,EAAE3B,eAAgB;IAC7B4B,SAAS,EAAEzB,aAAc;IACzB1D,QAAQ,EAAEA,QAAS;IACnB4E,KAAK,EAAED,oBAAoB,CAAC,CAAE;IAAAL,QAAA,gBAE9B7H,OAAA;MAAK2I,SAAS,EAAC,mBAAmB;MAAAd,QAAA,gBAC9B7H,OAAA;QAAK2I,SAAS,EAAC,cAAc;QAAAd,QAAA,gBACzB7H,OAAA;UAAA6H,QAAA,EAAI;QAA8B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvCjI,OAAA;UAAK2I,SAAS,EAAC,aAAa;UAAAd,QAAA,gBACxB7H,OAAA;YAAK2I,SAAS,EAAC,cAAc;YAAAd,QAAA,gBACzB7H,OAAA;cAAO4I,OAAO,EAAC,gBAAgB;cAAAf,QAAA,EAAC;YAAc;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtDjI,OAAA;cACImB,IAAI,EAAC,MAAM;cACX8D,IAAI,EAAC,gBAAgB;cACrB4B,EAAE,EAAC,gBAAgB;cACnB3C,KAAK,EAAEpD,OAAO,CAACE,cAAe;cAC9B6H,QAAQ,EAAE9D,kBAAmB;cAC7B+D,WAAW,EAAC;YAA+B;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENjI,OAAA;YAAK2I,SAAS,EAAC,cAAc;YAAAd,QAAA,gBACzB7H,OAAA;cAAO4I,OAAO,EAAC,qBAAqB;cAAAf,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAChEjI,OAAA;cACImB,IAAI,EAAC,MAAM;cACX8D,IAAI,EAAC,qBAAqB;cAC1B4B,EAAE,EAAC,qBAAqB;cACxB3C,KAAK,EAAEpD,OAAO,CAACG,mBAAoB;cACnC4H,QAAQ,EAAE9D,kBAAmB;cAC7B+D,WAAW,EAAC;YAAiB;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAENjI,OAAA;YAAK2I,SAAS,EAAC,cAAc;YAAAd,QAAA,gBACzB7H,OAAA;cAAO4I,OAAO,EAAC,QAAQ;cAAAf,QAAA,EAAC;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACtCjI,OAAA;cAAQiF,IAAI,EAAC,QAAQ;cAAC4B,EAAE,EAAC,QAAQ;cAAC3C,KAAK,EAAEpD,OAAO,CAACI,MAAO;cAAC2H,QAAQ,EAAE9D,kBAAmB;cAAA8C,QAAA,gBAClF7H,OAAA;gBAAQkE,KAAK,EAAC,EAAE;gBAAA2D,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAC5BnG,aAAa,CAACiH,GAAG,CAAEC,CAAC,iBACjBhJ,OAAA;gBAAqBkE,KAAK,EAAE8E,CAAC,CAAC/D,IAAK;gBAAA4C,QAAA,EAAEmB,CAAC,CAAC/D;cAAI,GAA9B+D,CAAC,CAAC/D,IAAI;gBAAA6C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAiC,CACvD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAENjI,OAAA;YAAK2I,SAAS,EAAC,cAAc;YAAAd,QAAA,gBACzB7H,OAAA;cAAO4I,OAAO,EAAC,YAAY;cAAAf,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACrDjI,OAAA;cACIiF,IAAI,EAAC,YAAY;cACjB4B,EAAE,EAAC,YAAY;cACf3C,KAAK,EAAEpD,OAAO,CAACM,UAAW;cAC1ByH,QAAQ,EAAE9D,kBAAmB;cAAA8C,QAAA,gBAE7B7H,OAAA;gBAAQkE,KAAK,EAAC,EAAE;gBAAA2D,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7BjI,OAAA;gBAAQkE,KAAK,EAAC,MAAM;gBAAA2D,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCjI,OAAA;gBAAQkE,KAAK,EAAC,OAAO;gBAAA2D,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAENjI,OAAA;YAAK2I,SAAS,EAAC,cAAc;YAAAd,QAAA,gBACzB7H,OAAA;cAAO4I,OAAO,EAAC,oBAAoB;cAAAf,QAAA,EAAC;YAAkB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC9DjI,OAAA;cACIiF,IAAI,EAAC,oBAAoB;cACzB4B,EAAE,EAAC,oBAAoB;cACvB3C,KAAK,EAAEpD,OAAO,CAACO,kBAAmB;cAClCwH,QAAQ,EAAE9D,kBAAmB;cAAA8C,QAAA,gBAE7B7H,OAAA;gBAAQkE,KAAK,EAAC,EAAE;gBAAA2D,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7BjI,OAAA;gBAAQkE,KAAK,EAAC,SAAS;gBAAA2D,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACxCjI,OAAA;gBAAQkE,KAAK,EAAC,aAAa;gBAAA2D,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAChDjI,OAAA;gBAAQkE,KAAK,EAAC,OAAO;gBAAA2D,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpCjI,OAAA;gBAAQkE,KAAK,EAAC,SAAS;gBAAA2D,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNjI,OAAA;UAAK2I,SAAS,EAAC,gBAAgB;UAAAd,QAAA,gBAC3B7H,OAAA;YAAQiJ,OAAO,EAAExF,eAAgB;YAACyF,QAAQ,EAAElH,OAAQ;YAAA6F,QAAA,EAC/C7F,OAAO,GAAG,YAAY,GAAG;UAAe;YAAA8F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrC,CAAC,eACTjI,OAAA;YACIiJ,OAAO,EAAEA,CAAA,KAAM;cACXlI,UAAU,CAAC;gBACPC,cAAc,EAAE,EAAE;gBAClBC,mBAAmB,EAAE,EAAE;gBACvBC,MAAM,EAAE,EAAE;gBACVC,IAAI,EAAE,EAAE;gBACRC,UAAU,EAAE,EAAE;gBACdC,kBAAkB,EAAE;cACxB,CAAC,CAAC;YACN,CAAE;YACF6H,QAAQ,EAAElH,OAAQ;YAAA6F,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,EAEL1H,cAAc,CAACyF,IAAI,GAAG,CAAC,iBACpBhG,OAAA;QAAK2I,SAAS,EAAC,YAAY;QAAAd,QAAA,GACtBnH,iBAAiB,KAAK,kBAAkB,iBACrCV,OAAA,CAAAE,SAAA;UAAA2H,QAAA,gBACI7H,OAAA;YAAQiJ,OAAO,EAAEA,CAAA,KAAMnD,gBAAgB,CAAC,qBAAqB,CAAE;YAACoD,QAAQ,EAAElH,OAAQ;YAAA6F,QAAA,EAAC;UAEnF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjI,OAAA;YAAQiJ,OAAO,EAAEA,CAAA,KAAMnD,gBAAgB,CAAC,sBAAsB,CAAE;YAACoD,QAAQ,EAAElH,OAAQ;YAAA6F,QAAA,EAAC;UAEpF;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTjI,OAAA;YAAQiJ,OAAO,EAAEA,CAAA,KAAM3F,qBAAqB,CAAC,IAAI,CAAE;YAAC4F,QAAQ,EAAElH,OAAQ;YAAA6F,QAAA,EAAC;UAEvE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACX,CACL,EACAvH,iBAAiB,KAAK,mBAAmB,iBACtCV,OAAA;UAAQiJ,OAAO,EAAEA,CAAA,KAAMnD,gBAAgB,CAAC,oBAAoB,CAAE;UAACoD,QAAQ,EAAElH,OAAQ;UAAA6F,QAAA,EAAC;QAElF;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACX;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACR,EAEAjG,OAAO,gBAAGhC,OAAA;QAAA6H,QAAA,EAAG;MAAU;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,gBACxBjI,OAAA,CAAAE,SAAA;QAAA2H,QAAA,gBACI7H,OAAA;UAAK2I,SAAS,EAAC,sBAAsB;UAAAd,QAAA,EAChCxH,UAAU,CAAC0I,GAAG,CAACV,aAAa,iBACzBrI,OAAA;YAAsC2I,SAAS,EAAC,mBAAmB;YAAAd,QAAA,gBAC/D7H,OAAA;cACI2I,SAAS,EAAC,kBAAkB;cAC5BM,OAAO,EAAEA,CAAA,KAAMtD,wBAAwB,CAAC0C,aAAa,CAACc,SAAS,CAACtC,EAAE,CAAE;cAAAgB,QAAA,gBAEpE7H,OAAA;gBAAA6H,QAAA,GACKQ,aAAa,CAACc,SAAS,CAAClE,IAAI,EAAC,QAAM,EAACoD,aAAa,CAACc,SAAS,CAACtC,EAAE,EAAC,GAChE,eAAA7G,OAAA;kBAAM2I,SAAS,EAAC,QAAQ;kBAAAd,QAAA,GAAC,gBACP,EAACQ,aAAa,CAACc,SAAS,CAACC,MAAM,CAACC,gBAAgB,EAAC,iBAClD,EAAChB,aAAa,CAACc,SAAS,CAACC,MAAM,CAACd,iBAAiB;gBAAA;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC5D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACLjI,OAAA;gBAAM2I,SAAS,EAAE,eAAe/H,kBAAkB,CAAC4E,GAAG,CAAC6C,aAAa,CAACc,SAAS,CAACtC,EAAE,CAAC,GAAG,UAAU,GAAG,EAAE,EAAG;gBAAAgB,QAAA,EAAC;cAExG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,EAELrH,kBAAkB,CAAC4E,GAAG,CAAC6C,aAAa,CAACc,SAAS,CAACtC,EAAE,CAAC,iBAC/C7G,OAAA;cAAK2I,SAAS,EAAC,mBAAmB;cAAAd,QAAA,gBAE9B7H,OAAA;gBAAK2I,SAAS,EAAC,YAAY;gBAAAd,QAAA,gBACvB7H,OAAA;kBAAA6H,QAAA,GAAI,oBAAkB,EAACQ,aAAa,CAACgB,gBAAgB,CAACC,MAAM,EAAC,GAAC;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnEjI,OAAA,CAACF,aAAa;kBACV+G,EAAE,EAAE,oBAAoBwB,aAAa,CAACc,SAAS,CAACtC,EAAE,EAAG;kBACrD8B,SAAS,EAAC,YAAY;kBACtBY,WAAW,EAAE,CAAC,mBAAmB,CAAE;kBAAA1B,QAAA,EAElCQ,aAAa,CAACgB,gBAAgB,CAACN,GAAG,CAACR,IAAI,iBACpCvI,OAAA,CAACH,kBAAkB;oBAEfgH,EAAE,EAAE0B,IAAI,CAAC1B,EAAG;oBACZ0B,IAAI,EAAEA,IAAK;oBACXiB,UAAU,EAAEjJ,cAAc,CAACiF,GAAG,CAAC+C,IAAI,CAAC1B,EAAE,CAAE;oBACxC4C,QAAQ,EAAErE,iBAAkB;oBAC5BsE,WAAW,EAAE9C,eAAgB;oBAC7B+C,YAAY,EAAErD,gBAAiB;oBAC/BhB,UAAU,EAAC,kBAAkB;oBAC7BM,WAAW,EAAEyC,aAAa,CAACc,SAAS,CAACtC,EAAG;oBACxCzE,QAAQ,EAAEA,QAAS;oBACnBwH,cAAc,EAAE;kBAAK,GAVhBrB,IAAI,CAAC1B,EAAE;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAWf,CACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACS,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eAGNjI,OAAA;gBAAK2I,SAAS,EAAC,YAAY;gBAAAd,QAAA,gBACvB7H,OAAA;kBAAA6H,QAAA,GAAI,qBAAmB,EAACQ,aAAa,CAACC,iBAAiB,CAACgB,MAAM,EAAC,GAAC;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrEjI,OAAA;kBAAK2I,SAAS,EAAC,YAAY;kBAAAd,QAAA,EACtBQ,aAAa,CAACC,iBAAiB,CAACS,GAAG,CAACR,IAAI,iBACrCvI,OAAA,CAACH,kBAAkB;oBAEfgH,EAAE,EAAE0B,IAAI,CAAC1B,EAAG;oBACZ0B,IAAI,EAAEA,IAAK;oBACXiB,UAAU,EAAEjJ,cAAc,CAACiF,GAAG,CAAC+C,IAAI,CAAC1B,EAAE,CAAE;oBACxC4C,QAAQ,EAAErE,iBAAkB;oBAC5BsE,WAAW,EAAE9C,eAAgB;oBAC7B+C,YAAY,EAAErD,gBAAiB;oBAC/BhB,UAAU,EAAC,mBAAmB;oBAC9BM,WAAW,EAAEyC,aAAa,CAACc,SAAS,CAACtC,EAAG;oBACxCzE,QAAQ,EAAEA,QAAS;oBACnBwH,cAAc,EAAE;kBAAM,GAVjBrB,IAAI,CAAC1B,EAAE;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAWf,CACJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CACR;UAAA,GAnEKI,aAAa,CAACc,SAAS,CAACtC,EAAE;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoE/B,CACR;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eACNjI,OAAA,CAACL,UAAU;UACPkK,WAAW,EAAEvI,UAAU,CAACE,IAAK;UAC7BsI,UAAU,EAAExI,UAAU,CAACI,WAAY;UACnCqI,YAAY,EAAExC;QAAiB;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC;MAAA,eACJ,CACL;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAENjI,OAAA,CAACR,QAAQ;MACLwK,IAAI,EAAEnH,YAAa;MACnBoH,KAAK,EAAEA,CAAA,KAAMnH,eAAe,CAAC,KAAK,CAAE;MACpCuD,MAAM,EAAEtD;IAAe;MAAA+E,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC,EACDhF,WAAW,iBACRjD,OAAA,CAACP,WAAW;MACR8G,KAAK,EAAEtD,WAAY;MACnB2C,WAAW,EAAEzC,iBAAkB;MAC/B+G,OAAO,EAAEA,CAAA,KAAM;QACXhH,cAAc,CAAC,IAAI,CAAC;QACpBE,oBAAoB,CAAC,IAAI,CAAC;MAC9B,CAAE;MACF+G,QAAQ,EAAEA,CAAA,KAAM;QACZjH,cAAc,CAAC,IAAI,CAAC;QACpBE,oBAAoB,CAAC,IAAI,CAAC;QAC1BK,eAAe,CAAC,CAAC;MACrB;IAAE;MAAAqE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACJ,EACA5E,kBAAkB,iBACfrD,OAAA,CAACN,YAAY;MACT0K,KAAK,EAAExI,cAAe;MACtBsI,OAAO,EAAEA,CAAA,KAAM5G,qBAAqB,CAAC,KAAK,CAAE;MAC5C+G,SAAS,EAAGC,YAAY,IAAK;QACzBxE,gBAAgB,CAAC,UAAU,EAAEwE,YAAY,CAAC;QAC1ChH,qBAAqB,CAAC,KAAK,CAAC;MAChC;IAAE;MAAAwE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CACJ;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAE1B,CAAC;AAAC7H,EAAA,CA1eID,OAAO;AAAAoK,EAAA,GAAPpK,OAAO;AA4eb,eAAeA,OAAO;AAAC,IAAAoK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}