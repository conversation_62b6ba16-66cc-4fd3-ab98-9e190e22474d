import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

const DraggableImageCard = ({ 
  id, 
  file, 
  isSelected, 
  onSelect, 
  onCardClick, 
  onImageClick, 
  subsection,
  plaintiffId,
  safeText,
  isDragDisabled = false
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ 
    id: id,
    disabled: isDragDisabled
  });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  // Only allow dragging from CN websites to copyrights files
  const canDrag = subsection === 'cn_websites_files' && !isDragDisabled;

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...(canDrag ? attributes : {})}
      {...(canDrag ? listeners : {})}
      className={`image-card ${isSelected ? 'selected' : ''} ${file.production ? 'production' : ''} ${isDragging ? 'dragging' : ''}`}
      onClick={() => onCardClick(file, plaintiffId)}
    >
      <div className="image-container-header">
        <input
          type="checkbox"
          checked={isSelected}
          onChange={() => onSelect(file.id, subsection)}
          onClick={(e) => e.stopPropagation()}
        />
        {canDrag && (
          <div className="drag-handle" title="Drag to move to Copyrights Files">
            ⋮⋮
          </div>
        )}
      </div>
      <div className="image-container" onClick={(e) => onImageClick(e, file)}>
        {file.low_res_path ? (
          <img src={file.low_res_path} alt={safeText(file.reg_no, 'N/A')} />
        ) : (
          <div className="no-image-placeholder">
            <span>No Viz</span>
          </div>
        )}
      </div>
      <div className="image-card-info">
        <p>
          <strong>
            {safeText(file.reg_no, 'N/A')}
            {file.method && ` (${safeText(file.method, 'N/A')})`}
          </strong>
        </p>
        {subsection === 'copyrights_files' && (
          <p>Type: {safeText(file.type, 'N/A')}</p>
        )}
        {subsection === 'cn_websites_files' && (
          <>
            <p>URL: {safeText(file.url, 'N/A')}</p>
            <p>Website: {safeText(file.website, 'N/A')}</p>
          </>
        )}
      </div>
    </div>
  );
};

export default DraggableImageCard;
