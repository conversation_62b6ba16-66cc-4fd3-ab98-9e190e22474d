import React from 'react';
import { useDroppable } from '@dnd-kit/core';

const DroppableZone = ({ id, children, className = '', acceptsFrom = [] }) => {
  const { isOver, setNodeRef } = useDroppable({
    id: id,
  });

  const style = {
    backgroundColor: isOver ? 'rgba(0, 123, 255, 0.1)' : undefined,
    border: isOver ? '2px dashed #007bff' : undefined,
    borderRadius: isOver ? '8px' : undefined,
    transition: 'all 0.2s ease',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`${className} ${isOver ? 'drop-zone-active' : ''}`}
    >
      {children}
      {isOver && acceptsFrom.length > 0 && (
        <div className="drop-indicator">
          <span>Drop here to move to Copyrights Files</span>
        </div>
      )}
    </div>
  );
};

export default DroppableZone;
