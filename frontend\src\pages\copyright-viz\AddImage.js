import React, { useState, useEffect } from 'react';
import { addCopyrightAsset, getCopyrightTypes, getCopyrightMethods } from '../../services/api_copyright_viz';

const fieldStyle = { display: 'flex', flexDirection: 'column', gap: 6, marginBottom: 14 };
const labelStyle = { fontWeight: 600, fontSize: 13, color: '#334155' };
const inputStyle = { padding: '10px 12px', borderRadius: 8, border: '1px solid #cbd5e1', fontSize: 14, outline: 'none' };
const selectStyle = inputStyle;
const buttonPrimary = { background: '#2563eb', color: 'white', border: 'none', padding: '10px 16px', borderRadius: 8, cursor: 'pointer', fontWeight: 600 };
const cardStyle = { maxWidth: 720, margin: '24px auto', background: 'white', border: '1px solid #e2e8f0', borderRadius: 12, boxShadow: '0 6px 20px rgba(2,6,23,0.06)' };
const headerStyle = { padding: '18px 20px', borderBottom: '1px solid #e2e8f0' };
const bodyStyle = { padding: 20, display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 20 };
const fullRow = { gridColumn: '1 / -1' };
const dropZoneStyle = { border: '2px dashed #94a3b8', borderRadius: 12, padding: 26, textAlign: 'center', color: '#475569', background: '#f8fafc' };
const helperStyle = { fontSize: 12, color: '#64748b' };
const errorStyle = { color: '#dc2626', background: '#fef2f2', border: '1px solid #fecaca', padding: '8px 10px', borderRadius: 8, margin: '0 20px 12px' };
const successStyle = { color: '#166534', background: '#ecfdf5', border: '1px solid #bbf7d0', padding: '8px 10px', borderRadius: 8, margin: '0 20px 12px' };

const AddImage = () => {
  const [file, setFile] = useState(null);
  const [registrationNumber, setRegistrationNumber] = useState('');
  const [plaintiffId, setPlaintiffId] = useState('');
  const [method, setMethod] = useState('');
  const [type, setType] = useState('');
  const [methodOptions, setMethodOptions] = useState([]);
  const [typeOptions, setTypeOptions] = useState([]);
  const [isProduction, setIsProduction] = useState(false);
  const [certificateStatus, setCertificateStatus] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  useEffect(() => {
    const loadOptions = async () => {
      try {
        const [typesRes, methodsRes] = await Promise.all([
          getCopyrightTypes(),
          getCopyrightMethods()
        ]);
        setTypeOptions(typesRes.data || []);
        setMethodOptions(methodsRes.data || []);
      } catch (e) {
        console.error('Failed to load options', e);
      }
    };
    loadOptions();
  }, []);

  const handleFileDrop = (e) => {
    e.preventDefault();
    const droppedFile = e.dataTransfer.files?.[0];
    if (droppedFile) setFile(droppedFile);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!file) { setError('Please select a file.'); return; }
    if (!plaintiffId) { setError('plaintiff_id is required.'); return; }
    if (!method) { setError('Method is required.'); return; }
    if (!type) { setError('Type is required.'); return; }

    setLoading(true);
    setError(null);
    setSuccess(null);

    const formData = new FormData();
    formData.append('file', file);
    if (registrationNumber) formData.append('registration_number', registrationNumber);
    formData.append('plaintiff_id', String(plaintiffId));
    formData.append('method', method);
    formData.append('type', type);
    formData.append('production', isProduction ? 'true' : 'false');
    if (certificateStatus) formData.append('certificate_status', certificateStatus);

    try {
      const result = await addCopyrightAsset(formData);
      setSuccess(`Asset added with ID: ${result.data.id}`);
      setFile(null);
      setRegistrationNumber('');
      setPlaintiffId('');
      setMethod('');
      setType('');
      setIsProduction(false);
      setCertificateStatus('');
    } catch (err) {
      setError(err.message || 'Failed to submit');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{ padding: 16 }}>
      <div style={cardStyle}>
        <div style={headerStyle}>
          <h1 style={{ margin: 0, fontSize: 20 }}>Add Image</h1>
        </div>
        {error && <div style={errorStyle}>{error}</div>}
        {success && <div style={successStyle}>{success}</div>}
        <form onSubmit={handleSubmit} style={{ paddingBottom: 16 }}>
          <div style={bodyStyle}>
            <div style={{ ...fieldStyle, ...fullRow }}>
              <label htmlFor="file-input" style={labelStyle}>Image file</label>
              <div
                className="drop-zone"
                onDrop={handleFileDrop}
                onDragOver={(e) => e.preventDefault()}
                style={dropZoneStyle}
                onClick={() => document.getElementById('file-input')?.click()}
              >
                {file ? <p>{file.name}</p> : <p>Drop file here or click to select</p>}
              </div>
              <input
                type="file"
                id="file-input"
                style={{ display: 'none' }}
                onChange={(e) => setFile(e.target.files?.[0] || null)}
                accept="image/*"
              />
              <div style={helperStyle}>Supported formats such as webp, jpg, png. One image per submission.</div>
            </div>

            <div style={fieldStyle}>
              <label style={labelStyle}>Registration Number</label>
              <input
                type="text"
                placeholder="Optional; autogenerated MD-... if empty"
                value={registrationNumber}
                onChange={(e) => setRegistrationNumber(e.target.value)}
                style={inputStyle}
              />
            </div>

            <div style={fieldStyle}>
              <label style={labelStyle}>Plaintiff ID</label>
              <input
                type="text"
                placeholder="Must exist in plaintiff cache"
                value={plaintiffId}
                onChange={(e) => setPlaintiffId(e.target.value)}
                style={inputStyle}
                required
              />
              <span style={helperStyle}>Enter an existing plaintiff_id from cache.</span>
            </div>

            <div style={fieldStyle}>
              <label style={labelStyle}>Method</label>
              <select value={method} onChange={(e) => setMethod(e.target.value)} style={selectStyle} required>
                <option value="" disabled>Select method</option>
                {methodOptions.map((m) => (
                  <option key={m.name} value={m.name}>{m.name}</option>
                ))}
              </select>
            </div>

            <div style={fieldStyle}>
              <label style={labelStyle}>Type</label>
              <select value={type} onChange={(e) => setType(e.target.value)} style={selectStyle} required>
                <option value="" disabled>Select type</option>
                {typeOptions.map((t) => (
                  <option key={t.name} value={t.name}>{t.name}</option>
                ))}
              </select>
            </div>

            <div style={fieldStyle}>
              <label style={labelStyle}>Certificate Status</label>
              <select value={certificateStatus} onChange={(e) => setCertificateStatus(e.target.value)} style={selectStyle}>
                <option value="">None</option>
                <option value="missing">Missing</option>
                <option value="fetched">Fetched</option>
                <option value="failed">Failed</option>
              </select>
            </div>

            <div style={{ ...fieldStyle, alignItems: 'flex-start' }}>
              <label style={labelStyle}>Production</label>
              <label style={{ display: 'flex', gap: 8, alignItems: 'center', fontSize: 14, color: '#334155' }}>
                <input
                  type="checkbox"
                  checked={isProduction}
                  onChange={(e) => setIsProduction(e.target.checked)}
                />
                Approve and generate vector immediately
              </label>
            </div>

            <div style={{ ...fullRow, display: 'flex', justifyContent: 'flex-end', marginTop: 4 }}>
              <button type="submit" style={buttonPrimary} disabled={loading}>
                {loading ? 'Submitting...' : 'Submit'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddImage;