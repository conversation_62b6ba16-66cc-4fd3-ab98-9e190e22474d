import React, { useState, useEffect, useCallback } from 'react';
import { getNextDuplicatePair, resolveDuplicatePair } from '../../services/api_copyright_viz';
import './DuplicateReviewer.css';

const AssetCard = ({ asset }) => {
    if (!asset) return null;
    return (
        <div className="asset-card">
            <div className="asset-image-container">
                <img src={asset.high_res_path} alt={asset.registration_number} />
            </div>
            <div className="asset-info">
                <h4>{asset.registration_number}</h4>
                <p><strong>Plaintiff:</strong> {asset.plaintiff_name || 'N/A'}</p>
                <p><strong>Method:</strong> {asset.method || 'N/A'}</p>
                <p><strong>Production:</strong> {asset.production ? 'Yes' : 'No'}</p>
            </div>
        </div>
    );
};

const DuplicateReviewer = () => {
    const [pair, setPair] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [resolving, setResolving] = useState(false);

    const fetchNextPair = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const result = await getNextDuplicatePair();
            // API might return empty success instead of error for no pairs
            if (result.data && result.data.left && result.data.right) {
                setPair(result.data);
            } else {
                setPair(null);
            }
        } catch (err) {
            // If the API returns a 501 Not Implemented, handle it gracefully
            if (err.message.includes('Not implemented')) {
                setError('The duplicate detection feature is not yet implemented in the backend.');
                setPair(null);
            } else {
                setError(err.message);
            }
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchNextPair();
    }, [fetchNextPair]);

    const handleResolve = async (action) => {
        if (!pair) return;

        let payload = { action };
        if (action === 'keep_left' || action === 'keep_right') {
            payload.keep_reg_no = action === 'keep_left' ? pair.left.registration_number : pair.right.registration_number;
            payload.discard_reg_no = action === 'keep_left' ? pair.right.registration_number : pair.left.registration_number;
        } else if (action === 'not_duplicate') {
            payload.keep_reg_no = pair.left.registration_number;
            payload.discard_reg_no = pair.right.registration_number;
        }

        setResolving(true);
        setError(null);
        try {
            await resolveDuplicatePair(payload);
            fetchNextPair(); // Fetch the next pair after resolving
        } catch (err) {
            setError(err.message);
        } finally {
            setResolving(false);
        }
    };

    const renderContent = () => {
        if (loading) {
            return <div className="loading-spinner"></div>;
        }
        if (error) {
            return <p className="error-message">{error}</p>;
        }
        if (!pair) {
            return (
                <div className="no-pairs-message">
                    <h2>All Clear!</h2>
                    <p>No duplicate pairs to review at the moment.</p>
                    <button onClick={fetchNextPair} disabled={loading}>Check for More</button>
                </div>
            );
        }
        return (
            <div className="reviewer-layout">
                <div className="comparison-area">
                    <AssetCard asset={pair.left} />
                    <div className="similarity-score">
                        <h3>Similarity</h3>
                        <p>{(pair.similarity * 100).toFixed(2)}%</p>
                    </div>
                    <AssetCard asset={pair.right} />
                </div>
                <div className="action-bar">
                    <button onClick={() => handleResolve('keep_left')} disabled={resolving} className="button-success">Keep Left</button>
                    <button onClick={() => handleResolve('not_duplicate')} disabled={resolving} className="button-secondary">Not a Duplicate</button>
                    <button onClick={() => handleResolve('keep_right')} disabled={resolving} className="button-success">Keep Right</button>
                </div>
            </div>
        );
    };

    return (
        <div className="duplicate-reviewer-container">
            <div className="reviewer-header">
                <h1>Duplicate Review</h1>
                <p>Review the asset pairs below and decide which one to keep.</p>
            </div>
            {renderContent()}
        </div>
    );
};

export default DuplicateReviewer;