.gallery-container {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.filter-panel {
    padding: 1rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.filter-panel h2 {
    margin-top: 0;
    margin-bottom: 1rem;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    margin-bottom: 0.25rem;
    font-weight: bold;
    font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
    padding: 0.5rem;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.filter-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    align-items: center;
}

.filter-actions button {
    padding: 0.5rem 1rem;
    border: 1px solid #007bff;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
}

.filter-actions button:first-child {
    background-color: #007bff;
    color: white;
}

.filter-actions button:first-child:hover:not(:disabled) {
    background-color: #0056b3;
}

.filter-actions button:last-child {
    background-color: white;
    color: #007bff;
}

.filter-actions button:last-child:hover:not(:disabled) {
    background-color: #f8f9fa;
}

.filter-actions button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.filter-toggles {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.toggle-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-bar {
    padding: 0.5rem 1rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

/* Ensure checkbox area sits above image and is clickable */
.image-container-header {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 3;
    display: flex;
    align-items: center;
    gap: 6px;
    pointer-events: auto;
}

.image-container-header input[type="checkbox"] {
    cursor: pointer;
}

/* Only show not-allowed when truly disabled AND attempting to click image area */
.image-card.disabled .image-container {
    cursor: default;
}

.image-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    padding: 1rem;
    overflow-y: auto;
    flex-grow: 1;
}

.image-card {
    border: 1px solid #ccc;
    border-radius: 8px;
    padding: 1rem;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    position: relative; /* allow header overlay */
}

.image-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.image-card.selected {
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
    background-color: #e7f3ff;
}

.image-card.disabled {
    cursor: default; /* only disable pointer on the card itself */
    opacity: 1; /* remove white veil effect on images/cards */
}

.image-card .image-container {
    width: 100%;
    padding-top: 100%; /* 1:1 Aspect Ratio */
    position: relative;
    margin-bottom: 0.5rem;
    background-color: #f0f0f0;
    border-radius: 4px;
    cursor: pointer; /* clickable area for lightbox */
}

.image-card img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
    opacity: 1; /* ensure no white veil */
    filter: none;
    pointer-events: none; /* allow container to handle click */
}

.no-image-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e9ecef;
    color: #6c757d;
    font-size: 0.9rem;
    border-radius: 4px;
    cursor: default;
}

.image-card-info {
    font-size: 0.9rem;
}

.image-card-info p {
    margin: 0.25rem 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.certificate-link {
    margin-top: 0.5rem;
    display: inline-block;
    font-size: 0.8rem;
}

/* Production cards have blue background */
.image-card.production {
    background-color: #1976d2;
    color: white;
}

/* Drag and Drop Styles */
.image-card.dragging {
    opacity: 0.5;
    transform: rotate(5deg);
}

.drag-handle {
    cursor: grab;
    color: #666;
    font-size: 1.2rem;
    padding: 0.25rem;
    user-select: none;
}

.drag-handle:hover {
    color: #333;
}

.drag-handle:active {
    cursor: grabbing;
}

.drop-zone-active {
    background-color: rgba(0, 123, 255, 0.1) !important;
    border: 2px dashed #007bff !important;
    border-radius: 8px !important;
}

.drop-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #007bff;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    font-weight: bold;
    z-index: 1000;
    pointer-events: none;
}

.drag-overlay {
    background-color: #f8f9fa;
    border: 2px solid #007bff;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    font-weight: bold;
    color: #007bff;
}

.image-container-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem;
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 4px 4px 0 0;
}

.image-card.production .image-card-info {
    color: white;
}

/* Plaintiff-based structure styles */
.plaintiffs-container {
    padding: 1rem;
}

.plaintiff-section {
    margin-bottom: 2rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
}

.plaintiff-header {
    background-color: #f8f9fa;
    padding: 1rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dee2e6;
}

.plaintiff-header:hover {
    background-color: #e9ecef;
}

.plaintiff-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.plaintiff-header .counts {
    font-weight: normal;
    font-size: 0.9rem;
    color: #6c757d;
}

.expand-icon {
    transition: transform 0.2s ease;
    font-size: 1.2rem;
}

.expand-icon.expanded {
    transform: rotate(180deg);
}

.plaintiff-content {
    padding: 1rem;
}

.subsection {
    margin-bottom: 2rem;
}

.subsection h4 {
    margin: 0 0 1rem 0;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-left: 4px solid #007bff;
    font-size: 1.1rem;
}

.subsection .image-grid {
    margin-top: 1rem;
}