import React, { useState, useEffect, useCallback, useRef } from 'react';
import { getCopyrightAssets, getCopyrightTypes, getCopyrightMethods, bulkUpdateCopyrightsFiles, moveCnWebsitesFiles } from '../../services/api_copyright_viz';
import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";
import './Gallery.css';
import ImageDrawer from './ImageDrawer';
import SetTypeModal from './SetTypeModal';
import Pagination from './Pagination';
import DragDropContext from '../../components/DragDropContext';
import DraggableImageCard from '../../components/DraggableImageCard';
import DroppableZone from '../../components/DroppableZone';

/**
 * Gallery component for browsing and managing copyright assets grouped by plaintiff.
 * It supports filtering, bulk actions, pagination, and detailed view of assets.
 */
const Gallery = () => {
    // State for plaintiffs data, selections, and UI controls
    const [plaintiffs, setPlaintiffs] = useState([]);
    const [selectedAssets, setSelectedAssets] = useState(new Set());
    const [currentSubsection, setCurrentSubsection] = useState('copyrights_files'); // Track which subsection is active for bulk actions
    const [expandedPlaintiffs, setExpandedPlaintiffs] = useState(new Set());
    const [filters, setFilters] = useState({
        plaintiff_name: '',
        registration_number: '',
        method: '',
        type: '',
        production: '',
        certificate_status: ''
    });
    const [pagination, setPagination] = useState({ page: 1, per_page: 5, total_pages: 1, total_items: 0 }); // 5 plaintiffs per page
    const [copyrightTypes, setCopyrightTypes] = useState([]);
    const [methodOptions, setMethodOptions] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    // Utility to safely coerce potentially NaN/undefined/null to displayable string
    const safeText = (val, fallback = 'N/A') => {
        if (val === null || val === undefined) return fallback;
        if (typeof val === 'number' && Number.isNaN(val)) return fallback;
        if (typeof val === 'string' && val.trim().toLowerCase() === 'nan') return fallback;
        return String(val);
    };

    // State for modals and drawers
    const [lightboxOpen, setLightboxOpen] = useState(false);
    const [lightboxSlides, setLightboxSlides] = useState([]);
    const [drawerAsset, setDrawerAsset] = useState(null);
    const [drawerPlaintiffId, setDrawerPlaintiffId] = useState(null);
    const [isSetTypeModalOpen, setIsSetTypeModalOpen] = useState(false);

    // Drag and drop state
    const [activeId, setActiveId] = useState(null);

    // Initial data load
    useEffect(() => {
        const fetchInitialData = async () => {
            setLoading(true);
            setError(null);
            try {
                const [typesResult, methodsResult, plaintiffsResult] = await Promise.all([
                    getCopyrightTypes(),
                    getCopyrightMethods(),
                    getCopyrightAssets({ page: 1, per_page: 5 }) // Initial fetch
                ]);
                setCopyrightTypes(typesResult.data || []);
                setMethodOptions(methodsResult.data || []);
                setPlaintiffs(plaintiffsResult?.data?.plaintiffs ?? []);
                setPagination(plaintiffsResult.data.pagination);
            } catch (err) {
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };
        fetchInitialData();
    }, []); // Empty dependency array ensures this runs only once on mount

    // Subsequent data fetching based on filters and pagination
    const fetchPlaintiffs = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const activeFilters = Object.entries(filters).reduce((acc, [key, value]) => {
                if (value !== '') acc[key] = value;
                return acc;
            }, {});
            const params = { ...activeFilters, page: pagination.page, per_page: pagination.per_page };
            const result = await getCopyrightAssets(params);
            setPlaintiffs(result?.data?.plaintiffs ?? []);
            setPagination(result.data.pagination);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    }, [filters, pagination.page, pagination.per_page]); // Dependencies for re-fetching

    // Effect for applying filters
    useEffect(() => {
        // We don't want to run this on initial mount
        // The initial data is already loaded by the first useEffect
        const initialLoad = pagination.page === 1 && Object.values(filters).every(v => v === '');
        if (!initialLoad) {
            fetchPlaintiffs();
        }
    }, [filters, pagination.page, fetchPlaintiffs]);

    /**
     * Handles changes in filter inputs.
     */
    const handleFilterChange = (e) => {
        const { name, value } = e.target;
        setFilters(prev => ({
            ...prev,
            [name]: value,
        }));
    };

    /**
     * Toggles the selection of an asset.
     */
    const handleSelectAsset = (assetId, subsection) => {
        if (!assetId) return;
        const newSelectedAssets = new Set(selectedAssets);
        if (newSelectedAssets.has(assetId)) {
            newSelectedAssets.delete(assetId);
        } else {
            newSelectedAssets.add(assetId);
        }
        setSelectedAssets(newSelectedAssets);
        setCurrentSubsection(subsection); // Track which subsection the selection is from
    };

    /**
     * Toggles the expansion state of a plaintiff section.
     */
    const togglePlaintiffExpansion = (plaintiffId) => {
        const newExpanded = new Set(expandedPlaintiffs);
        if (newExpanded.has(plaintiffId)) {
            newExpanded.delete(plaintiffId);
        } else {
            newExpanded.add(plaintiffId);
        }
        setExpandedPlaintiffs(newExpanded);
    };

    /**
     * Executes a bulk action on the selected assets.
     */
    const handleBulkAction = async (action, value = null) => {
        if (selectedAssets.size === 0) return;

        setLoading(true);
        setError(null);
        try {
            const selectedIds = Array.from(selectedAssets);

            if (currentSubsection === 'copyrights_files') {
                // Handle copyrights_files bulk actions
                if (action === 'set_production_true') {
                    await bulkUpdateCopyrightsFiles('set_production', selectedIds, true);
                } else if (action === 'set_production_false') {
                    await bulkUpdateCopyrightsFiles('set_production', selectedIds, false);
                } else if (action === 'set_type') {
                    await bulkUpdateCopyrightsFiles('set_type', selectedIds, value);
                }
            } else if (currentSubsection === 'cn_websites_files') {
                // Handle cn_websites_files bulk actions
                if (action === 'move_to_copyrights') {
                    await moveCnWebsitesFiles(selectedIds);
                }
            }

            setSelectedAssets(new Set());
            fetchPlaintiffs();
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    /**
     * Opens the lightbox with the specified slides.
     */
    const openLightbox = (slides) => {
        setLightboxSlides(slides);
        setLightboxOpen(true);
    };

    /**
     * Handles clicks on the main image of an asset card.
     */
    const handleImageClick = (e, asset) => {
        e.stopPropagation();
        if (asset.high_res_path) {
            openLightbox([{ src: asset.high_res_path, title: safeText(asset.registration_number, 'N/A') }]);
        }
    };

    /**
     * Handles clicks on an asset card to open the details drawer.
     */
    const handleCardClick = (asset, plaintiffId) => {
        if (asset.id) {
            setDrawerAsset(asset);
            setDrawerPlaintiffId(plaintiffId);
        }
    };

    /**
     * Handles drag start event
     */
    const handleDragStart = (event) => {
        setActiveId(event.active.id);
    };

    /**
     * Handles drag end event - moves CN websites files to copyrights files
     */
    const handleDragEnd = async (event) => {
        const { active, over } = event;
        setActiveId(null);

        if (!over) return;

        // Check if we're dropping a CN websites file onto a copyrights files zone
        if (active.id && over.id && over.id.startsWith('copyrights-files-')) {
            const draggedFileId = active.id;
            const targetPlaintiffId = over.id.replace('copyrights-files-', '');

            try {
                setLoading(true);
                await moveCnWebsitesFiles([draggedFileId]);
                await fetchPlaintiffs(); // Refresh data
            } catch (err) {
                setError(err.message);
            } finally {
                setLoading(false);
            }
        }
    };

    /**
     * Handles page changes from the pagination component.
     */
    const handlePageChange = (newPage) => {
        setPagination(prev => ({ ...prev, page: newPage }));
    };

    /**
     * Handles clicks on the "View Certificate" link.
     */
    const handleCertificateClick = (e, asset) => {
        e.stopPropagation();
        if (asset.certificate_path) {
            openLightbox([{ src: asset.certificate_path, title: `Certificate for ${safeText(asset.registration_number, 'N/A')}` }]);
        }
    };

    if (error) return <p style={{ color: 'red' }}>{error}</p>;

    // Get all draggable items for DndContext
    const getAllDraggableItems = () => {
        const items = [];
        plaintiffs.forEach(plaintiffData => {
            plaintiffData.cn_websites_files.forEach(file => {
                items.push(file.id);
            });
        });
        return items;
    };

    return (
        <DragDropContext
            onDragStart={handleDragStart}
            onDragEnd={handleDragEnd}
            activeId={activeId}
            items={getAllDraggableItems()}
        >
            <div className="gallery-container">
                <div className="filter-panel">
                    <h2>Copyright Gallery v2 - Filters</h2>
                    <div className="filter-grid">
                        <div className="filter-group">
                            <label htmlFor="plaintiff_name">Plaintiff Name</label>
                            <input
                                type="text"
                                name="plaintiff_name"
                                id="plaintiff_name"
                                value={filters.plaintiff_name}
                                onChange={handleFilterChange}
                                placeholder="Fuzzy match, case-insensitive"
                            />
                        </div>

                        <div className="filter-group">
                            <label htmlFor="registration_number">Registration Number</label>
                            <input
                                type="text"
                                name="registration_number"
                                id="registration_number"
                                value={filters.registration_number}
                                onChange={handleFilterChange}
                                placeholder="e.g., VA 123456"
                            />
                        </div>

                        <div className="filter-group">
                            <label htmlFor="method">Method</label>
                            <select name="method" id="method" value={filters.method} onChange={handleFilterChange}>
                                <option value="">All</option>
                                {methodOptions.map((m) => (
                                    <option key={m.name} value={m.name}>{m.name}</option>
                                ))}
                            </select>
                        </div>

                        <div className="filter-group">
                            <label htmlFor="production">Production Status</label>
                            <select
                                name="production"
                                id="production"
                                value={filters.production}
                                onChange={handleFilterChange}
                            >
                                <option value="">All</option>
                                <option value="true">Production</option>
                                <option value="false">Non-Production</option>
                            </select>
                        </div>

                        <div className="filter-group">
                            <label htmlFor="certificate_status">Certificate Status</label>
                            <select
                                name="certificate_status"
                                id="certificate_status"
                                value={filters.certificate_status}
                                onChange={handleFilterChange}
                            >
                                <option value="">All</option>
                                <option value="fetched">Fetched</option>
                                <option value="not_fetched">Not Fetched</option>
                                <option value="error">Error</option>
                                <option value="pending">Pending</option>
                            </select>
                        </div>
                    </div>
                    <div className="filter-actions">
                        <button onClick={fetchPlaintiffs} disabled={loading}>
                            {loading ? 'Loading...' : 'Apply Filters'}
                        </button>
                        <button
                            onClick={() => {
                                setFilters({
                                    plaintiff_name: '',
                                    registration_number: '',
                                    method: '',
                                    type: '',
                                    production: '',
                                    certificate_status: ''
                                });
                            }}
                            disabled={loading}
                        >
                            Clear Filters
                        </button>
                    </div>
                </div>
                {/* Bulk Action Toolbar - contextual to current subsection */}
                {selectedAssets.size > 0 && (
                    <div className="action-bar">
                        {currentSubsection === 'copyrights_files' && (
                            <>
                                <button onClick={() => handleBulkAction('set_production_true')} disabled={loading}>
                                    Set Production True
                                </button>
                                <button onClick={() => handleBulkAction('set_production_false')} disabled={loading}>
                                    Set Production False
                                </button>
                                <button onClick={() => setIsSetTypeModalOpen(true)} disabled={loading}>
                                    Set Type
                                </button>
                            </>
                        )}
                        {currentSubsection === 'cn_websites_files' && (
                            <button onClick={() => handleBulkAction('move_to_copyrights')} disabled={loading}>
                                Move to Copyrights Files
                            </button>
                        )}
                    </div>
                )}

                {loading ? <p>Loading...</p> : (
                    <>
                        <div className="plaintiffs-container">
                            {plaintiffs.map(plaintiffData => (
                                <div key={plaintiffData.plaintiff.id} className="plaintiff-section">
                                    <div
                                        className="plaintiff-header"
                                        onClick={() => togglePlaintiffExpansion(plaintiffData.plaintiff.id)}
                                    >
                                        <h3>
                                            {plaintiffData.plaintiff.name} (ID: {plaintiffData.plaintiff.id})
                                            <span className="counts">
                                                - Copyrights: {plaintiffData.plaintiff.counts.copyrights_files},
                                                CN Websites: {plaintiffData.plaintiff.counts.cn_websites_files}
                                            </span>
                                        </h3>
                                        <span className={`expand-icon ${expandedPlaintiffs.has(plaintiffData.plaintiff.id) ? 'expanded' : ''}`}>
                                            ▼
                                        </span>
                                    </div>

                                    {expandedPlaintiffs.has(plaintiffData.plaintiff.id) && (
                                        <div className="plaintiff-content">
                                            {/* Copyrights Files Subsection */}
                                            <div className="subsection">
                                                <h4>Copyrights Files ({plaintiffData.copyrights_files.length})</h4>
                                                <DroppableZone
                                                    id={`copyrights-files-${plaintiffData.plaintiff.id}`}
                                                    className="image-grid"
                                                    acceptsFrom={['cn_websites_files']}
                                                >
                                                    {plaintiffData.copyrights_files.map(file => (
                                                        <DraggableImageCard
                                                            key={file.id}
                                                            id={file.id}
                                                            file={file}
                                                            isSelected={selectedAssets.has(file.id)}
                                                            onSelect={handleSelectAsset}
                                                            onCardClick={handleCardClick}
                                                            onImageClick={handleImageClick}
                                                            subsection="copyrights_files"
                                                            plaintiffId={plaintiffData.plaintiff.id}
                                                            safeText={safeText}
                                                            isDragDisabled={true}
                                                        />
                                                    ))}
                                                </DroppableZone>
                                            </div>

                                            {/* CN Websites Files Subsection */}
                                            <div className="subsection">
                                                <h4>CN Websites Files ({plaintiffData.cn_websites_files.length})</h4>
                                                <div className="image-grid">
                                                    {plaintiffData.cn_websites_files.map(file => (
                                                        <DraggableImageCard
                                                            key={file.id}
                                                            id={file.id}
                                                            file={file}
                                                            isSelected={selectedAssets.has(file.id)}
                                                            onSelect={handleSelectAsset}
                                                            onCardClick={handleCardClick}
                                                            onImageClick={handleImageClick}
                                                            subsection="cn_websites_files"
                                                            plaintiffId={plaintiffData.plaintiff.id}
                                                            safeText={safeText}
                                                            isDragDisabled={false}
                                                        />
                                                    ))}
                                                </div>
                                            </div>
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                        <Pagination
                            currentPage={pagination.page}
                            totalPages={pagination.total_pages}
                            onPageChange={handlePageChange}
                        />
                    </>
                )}
            </div>
            {/* Modals and Drawers */}
            <Lightbox
                open={lightboxOpen}
                close={() => setLightboxOpen(false)}
                slides={lightboxSlides}
            />
            {drawerAsset && (
                <ImageDrawer
                    asset={drawerAsset}
                    plaintiffId={drawerPlaintiffId}
                    onClose={() => {
                        setDrawerAsset(null);
                        setDrawerPlaintiffId(null);
                    }}
                    onUpdate={() => {
                        setDrawerAsset(null);
                        setDrawerPlaintiffId(null);
                        fetchPlaintiffs();
                    }}
                />
            )}
            {isSetTypeModalOpen && (
                <SetTypeModal
                    types={copyrightTypes}
                    onClose={() => setIsSetTypeModalOpen(false)}
                    onConfirm={(selectedType) => {
                        handleBulkAction('set_type', selectedType);
                        setIsSetTypeModalOpen(false);
                    }}
                />
            )}
        </DragDropContext>
    );
};

export default Gallery;