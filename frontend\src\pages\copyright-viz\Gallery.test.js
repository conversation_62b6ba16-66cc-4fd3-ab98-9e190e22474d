import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import Gallery from './Gallery';
import * as api_copyright_viz from '../../services/api_copyright_viz';

// Mock the services
jest.mock('../../services/api_copyright_viz');

const mockAssets = {
    data: {
        assets: [
            { viz_id: 1, copyright_id: 101, registration_number: 'VA 123', plaintiff_name: 'Test Plaintiff', production: true, method: 'exhibit', high_res_path: 'high/1.jpg' },
            { viz_id: 2, copyright_id: 102, registration_number: 'VA 456', plaintiff_name: 'Test Plaintiff 2', production: false, method: 'manual', high_res_path: 'high/2.jpg' },
        ],
        pagination: { page: 1, per_page: 50, total_pages: 1, total_items: 2 }
    }
};

const mockEmptyAssets = {
    data: {
        assets: [],
        pagination: { page: 1, per_page: 50, total_pages: 0, total_items: 0 }
    }
};

describe('Gallery Component', () => {
    beforeEach(() => {
        api_copyright_viz.getCopyrightAssets.mockResolvedValue(mockAssets);
        api_copyright_viz.getCopyrightTypes.mockResolvedValue({ data: [{ id: 1, name: 'Type A' }] });
        api_copyright_viz.bulkUpdateCopyrightAssets.mockResolvedValue({ success: true });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('renders loading state initially', () => {
        render(<Gallery />);
        expect(screen.getByText(/Loading assets.../i)).toBeInTheDocument();
    });

    it('renders assets after successful fetch', async () => {
        render(<Gallery />);
        await waitFor(() => {
            expect(screen.getByText('VA 123')).toBeInTheDocument();
            expect(screen.getByText('VA 456')).toBeInTheDocument();
        });
    });

    it('renders error state on fetch failure', async () => {
        api_copyright_viz.getCopyrightAssets.mockRejectedValue(new Error('Failed to fetch'));
        render(<Gallery />);
        await waitFor(() => {
            expect(screen.getByText(/Error: Failed to fetch/i)).toBeInTheDocument();
        });
    });

    it('renders no assets message when fetch returns empty', async () => {
        api_copyright_viz.getCopyrightAssets.mockResolvedValue(mockEmptyAssets);
        render(<Gallery />);
        await waitFor(() => {
            expect(screen.getByText(/No assets found./i)).toBeInTheDocument();
        });
    });

    it('calls fetch function with correct filters when filter is changed', async () => {
        render(<Gallery />);
        await waitFor(() => expect(api_copyright_viz.getCopyrightAssets).toHaveBeenCalledTimes(1));

        const regNoInput = screen.getByPlaceholderText(/Reg No/i);
        fireEvent.change(regNoInput, { target: { value: 'VA 123' } });
        fireEvent.keyDown(regNoInput, { key: 'Enter', code: 'Enter' });

        await waitFor(() => {
            expect(api_copyright_viz.getCopyrightAssets).toHaveBeenCalledWith(expect.objectContaining({
                registration_number: 'VA 123'
            }));
        });
    });

    it('opens and closes the ImageDrawer when an image is clicked', async () => {
        render(<Gallery />);
        await waitFor(() => screen.getByText('VA 123'));

        fireEvent.click(screen.getByText('VA 123'));
        
        await waitFor(() => {
            expect(screen.getByRole('heading', { name: 'VA 123' })).toBeInTheDocument();
        });

        fireEvent.click(screen.getByLabelText('Close Drawer'));
        
        await waitFor(() => {
            expect(screen.queryByRole('heading', { name: 'VA 123' })).not.toBeInTheDocument();
        });
    });

    it('handles bulk actions correctly', async () => {
        render(<Gallery />);
        await waitFor(() => screen.getByText('VA 123'));

        const checkbox1 = screen.getByLabelText(/select asset VA 123/i);
        fireEvent.click(checkbox1);

        const approveButton = screen.getByRole('button', { name: /Approve/i });
        fireEvent.click(approveButton);

        await waitFor(() => {
            expect(api_copyright_viz.bulkUpdateCopyrightAssets).toHaveBeenCalledWith({
                asset_ids: [1],
                action: 'approve'
            });
        });
    });
});