.drawer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    justify-content: flex-end;
}

.drawer-panel {
    width: 500px;
    max-width: 90vw;
    height: 100%;
    background-color: white;
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    animation: slide-in 0.3s forwards;
}

@keyframes slide-in {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

.drawer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.drawer-header h3 {
    margin: 0;
}

.drawer-close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
}

.drawer-tabs {
    display: flex;
    border-bottom: 1px solid #e9ecef;
}

.drawer-tab {
    padding: 0.75rem 1.25rem;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    margin-bottom: -1px;
}

.drawer-tab.active {
    border-bottom-color: #007bff;
    color: #007bff;
}

.drawer-content {
    padding: 1rem;
    overflow-y: auto;
    flex-grow: 1;
}
/* --- Info Tab --- */
.info-tab-content .form-group {
    margin-bottom: 1rem;
}

.info-tab-content label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.info-tab-content input[type="text"],
.info-tab-content select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}

.info-tab-content input[type="checkbox"] {
    margin-right: 10px;
}

.reg-no-display {
    display: flex;
    align-items: center;
    gap: 10px;
}

.reg-no-display input {
    flex-grow: 1;
}

.button-promote {
    padding: 8px 12px;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    white-space: nowrap;
}

.button-promote:hover {
    background-color: #218838;
}


/* --- Crop Tab --- */
.crop-tab-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.crop-tab-content .ReactCrop {
    margin-bottom: 1rem;
}

/* --- Modal (for Promote) --- */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 1050;
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-panel {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    width: 500px;
    max-width: 90%;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 1rem;
    margin-bottom: 1rem;
}

.modal-header h3 {
    margin: 0;
}

.modal-close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

.error-message {
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    padding: 0.75rem 1.25rem;
    margin-top: 1rem;
    border-radius: 0.25rem;
}

/* CN Websites Files Info Tab Styles */
.cn-websites-info {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.info-row {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
}

.info-row label {
    font-weight: bold;
    min-width: 120px;
    flex-shrink: 0;
}

.info-row span {
    flex: 1;
    word-break: break-word;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.url-text {
    font-size: 0.9rem;
    color: #6c757d;
}

.copy-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.2rem;
    border-radius: 0.25rem;
    font-size: 0.8rem;
}

.copy-btn:hover {
    background-color: #f8f9fa;
}

.move-action {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #dee2e6;
}

.move-button {
    width: 100%;
    padding: 0.75rem;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.move-button.primary {
    background-color: #007bff;
    color: white;
}

.move-button.primary:hover:not(:disabled) {
    background-color: #0056b3;
}

.move-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Tools Tab Styles */
.tools-tab-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.tool-section {
    padding: 1rem;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

.tool-section h4 {
    margin: 0 0 0.5rem 0;
    color: #495057;
}

.tool-button {
    padding: 0.5rem 1rem;
    border: 1px solid #007bff;
    background-color: #007bff;
    color: white;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.tool-button:hover:not(:disabled) {
    background-color: #0056b3;
}

.tool-button.disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    cursor: not-allowed;
}

.split-editor {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
}

.split-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.split-actions button {
    padding: 0.5rem 1rem;
    border: 1px solid #dee2e6;
    background-color: white;
    border-radius: 0.375rem;
    cursor: pointer;
}

.split-actions button:first-child {
    background-color: #28a745;
    color: white;
    border-color: #28a745;
}

/* Registration Number Allocation Tool Styles */
.reg-allocation-tool {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.reg-allocation-tool h4 {
    margin: 0;
    color: #495057;
}

.current-assignment {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
}

.current-assignment label {
    font-weight: bold;
}

.search-select {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.search-input {
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}

.reg-select {
    min-height: 120px;
    padding: 0.5rem;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
}

.reg-select option {
    padding: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.assign-button {
    padding: 0.75rem;
    background-color: #28a745;
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.assign-button:hover:not(:disabled) {
    background-color: #218838;
}

.assign-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Preview Tab Styles */
.preview-tab-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.image-preview img {
    max-width: 100%;
    height: auto;
    border-radius: 0.375rem;
}

.certificate-preview {
    border-top: 1px solid #dee2e6;
    padding-top: 1rem;
}

.certificate-preview h4 {
    margin: 0 0 1rem 0;
    color: #495057;
}

.empty-state {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.attach-certificate-btn {
    padding: 0.5rem 1rem;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    margin-top: 0.5rem;
}