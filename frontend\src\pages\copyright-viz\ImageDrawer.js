import React, { useState, useEffect, useRef } from 'react';
import ReactCrop, { centerCrop, makeAspectCrop } from 'react-image-crop';
import 'react-image-crop/dist/ReactCrop.css';
import { getCopyrightTypes, updateCopyrightAsset, cropCopyrightAsset, promoteCopyrightAsset } from '../../services/api_copyright_viz';
import './ImageDrawer.css';

const PromoteModal = ({ asset, onClose, onUpdate }) => {
    const [newRegNo, setNewRegNo] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (!newRegNo) {
            setError('New registration number cannot be empty.');
            return;
        }
        setLoading(true);
        setError(null);
        try {
            // Promote endpoint expects integer id path param (<int:id>/promote). Use id.
            await promoteCopyrightAsset(asset.id, newRegNo);
            onUpdate();
            onClose();
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="modal-overlay">
            <div className="modal-panel">
                <div className="modal-header">
                    <h3>Promote Asset</h3>
                    <button onClick={onClose} className="modal-close-btn">&times;</button>
                </div>
                <div className="modal-content">
                    <p>
                        Promoting asset <strong>{asset.registration_number}</strong>.
                        Please enter the official registration number below.
                    </p>
                    <form onSubmit={handleSubmit}>
                        <div className="form-group">
                            <label htmlFor="newRegNo">New Registration Number</label>
                            <input
                                id="newRegNo"
                                type="text"
                                value={newRegNo}
                                onChange={(e) => setNewRegNo(e.target.value)}
                                placeholder="e.g., VA 1-234-567"
                                required
                            />
                        </div>
                        {error && <p className="error-message">{error}</p>}
                        <div className="modal-actions">
                            <button type="button" onClick={onClose} className="button-secondary">Cancel</button>
                            <button type="submit" disabled={loading} className="button-primary">
                                {loading ? 'Promoting...' : 'Promote'}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};


// Info tab for copyrights_files
const CopyrightsFilesInfoTab = ({ asset, onUpdate }) => {
    const [types, setTypes] = useState([]);
    const [methods, setMethods] = useState([]);
    const [editableAsset, setEditableAsset] = useState(asset);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchData = async () => {
            try {
                const [typesResult, methodsResult] = await Promise.all([
                    getCopyrightTypes(),
                    getCopyrightMethods()
                ]);
                setTypes(typesResult.data || []);
                setMethods(methodsResult.data || []);
            } catch (err) {
                console.error("Failed to fetch types/methods", err);
            }
        };
        fetchData();
    }, []);

    useEffect(() => {
        setEditableAsset(asset);
    }, [asset]);

    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        setEditableAsset(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));
    };

    const handleUpdate = async (fieldName, value) => {
        setLoading(true);
        setError(null);
        try {
            await updateCopyrightAsset(asset.id, { [fieldName]: value });
            onUpdate();
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="info-tab-content">
            {error && <p className="error-message">{error}</p>}

            <div className="form-group">
                <label>Registration Number</label>
                <div className="reg-no-display">
                    <input type="text" value={editableAsset.registration_number || ''} readOnly disabled />
                    {editableAsset.registration_number && editableAsset.registration_number.startsWith('MD-') && (
                        <button onClick={() => onUpdate('promote')} className="button-promote">Promote</button>
                    )}
                </div>
            </div>

            <div className="form-group">
                <label>Filename</label>
                <input type="text" value={editableAsset.filename || ''} readOnly disabled />
            </div>

            <div className="form-group">
                <label>Dimensions</label>
                <input type="text" value="N/A" readOnly disabled />
            </div>

            <div className="form-group">
                <label>Created At</label>
                <input type="text" value={editableAsset.create_time || 'N/A'} readOnly disabled />
            </div>

            <div className="form-group">
                <label>Updated At</label>
                <input type="text" value={editableAsset.update_time || 'N/A'} readOnly disabled />
            </div>

            <div className="form-group">
                <label htmlFor="production">Production Flag</label>
                <input
                    type="checkbox"
                    id="production"
                    name="production"
                    checked={editableAsset.production || false}
                    onChange={(e) => {
                        handleInputChange(e);
                        handleUpdate('production', e.target.checked);
                    }}
                    disabled={loading}
                />
            </div>

            <div className="form-group">
                <label htmlFor="method">Method</label>
                <select
                    id="method"
                    name="method"
                    value={editableAsset.method || ''}
                    onChange={(e) => {
                        handleInputChange(e);
                        handleUpdate('method', e.target.value);
                    }}
                    disabled={loading}
                >
                    <option value="">Select Method</option>
                    {methods.map((m) => (
                        <option key={m.name} value={m.name}>{m.name}</option>
                    ))}
                </select>
            </div>

            <div className="form-group">
                <label htmlFor="type">Type</label>
                <select
                    id="type"
                    name="type"
                    value={editableAsset.type || ''}
                    onChange={(e) => {
                        handleInputChange(e);
                        handleUpdate('type', e.target.value);
                    }}
                    disabled={loading}
                >
                    <option value="">Select Type</option>
                    {types.map((t) => (
                        <option key={t.name} value={t.name}>{t.name}</option>
                    ))}
                </select>
            </div>
        </div>
    );
};

// Info tab for cn_websites_files
const CnWebsitesFilesInfoTab = ({ asset, onUpdate }) => {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const handleMoveTocopyrights = async () => {
        setLoading(true);
        setError(null);
        try {
            await moveCnWebsitesFiles([asset.id]);
            onUpdate(); // Refresh the gallery
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    const copyToClipboard = (text) => {
        navigator.clipboard.writeText(text);
    };

    return (
        <div className="info-tab-content">
            {error && <p className="error-message">{error}</p>}

            <div className="cn-websites-info">
                <div className="info-row">
                    <label>Registration Number:</label>
                    <span>{asset.reg_no || 'N/A'}
                        <button onClick={() => copyToClipboard(asset.reg_no || '')} className="copy-btn">📋</button>
                    </span>
                </div>

                <div className="info-row">
                    <label>Docket in Title:</label>
                    <span>{asset.docket_in_title || 'N/A'}
                        <button onClick={() => copyToClipboard(asset.docket_in_title || '')} className="copy-btn">📋</button>
                    </span>
                </div>

                <div className="info-row">
                    <label>Docket Formatted:</label>
                    <span>{asset.docket_formatted || 'N/A'}
                        <button onClick={() => copyToClipboard(asset.docket_formatted || '')} className="copy-btn">📋</button>
                    </span>
                </div>

                <div className="info-row">
                    <label>Case ID:</label>
                    <span>{asset.case_id || 'N/A'}
                        <button onClick={() => copyToClipboard(asset.case_id?.toString() || '')} className="copy-btn">📋</button>
                    </span>
                </div>

                <div className="info-row">
                    <label>URL:</label>
                    <span className="url-text">{asset.url || 'N/A'}
                        <button onClick={() => copyToClipboard(asset.url || '')} className="copy-btn">📋</button>
                    </span>
                </div>

                <div className="info-row">
                    <label>Type:</label>
                    <span>{asset.type || 'N/A'}
                        <button onClick={() => copyToClipboard(asset.type || '')} className="copy-btn">📋</button>
                    </span>
                </div>

                <div className="info-row">
                    <label>Website:</label>
                    <span>{asset.website || 'N/A'}
                        <button onClick={() => copyToClipboard(asset.website || '')} className="copy-btn">📋</button>
                    </span>
                </div>

                <div className="info-row">
                    <label>Site Domain:</label>
                    <span>{asset.site_domain || 'N/A'}
                        <button onClick={() => copyToClipboard(asset.site_domain || '')} className="copy-btn">📋</button>
                    </span>
                </div>

                <div className="info-row">
                    <label>Post Date:</label>
                    <span>{asset.post_date || 'N/A'}
                        <button onClick={() => copyToClipboard(asset.post_date || '')} className="copy-btn">📋</button>
                    </span>
                </div>
            </div>

            <div className="move-action">
                <button
                    onClick={handleMoveTocopyrights}
                    disabled={loading}
                    className="move-button primary"
                >
                    {loading ? 'Moving...' : 'Move to Copyrights Files'}
                </button>
            </div>
        </div>
    );
};

// Tools tab for copyrights_files
const CopyrightsFilesToolsTab = ({ asset, onUpdate }) => {
    const [splitMode, setSplitMode] = useState(false);
    const [splitRegions, setSplitRegions] = useState([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const handleSplitClick = () => {
        setSplitMode(true);
        // Initialize with one region
        setSplitRegions([{ x: 10, y: 10, width: 100, height: 100 }]);
    };

    const handleSplitSave = async () => {
        setLoading(true);
        setError(null);
        try {
            const inherit = {
                reg_no: true,
                type: true,
                production: true
            };
            await splitCopyrightFile(asset.id, splitRegions, inherit);
            onUpdate();
            setSplitMode(false);
            setSplitRegions([]);
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="tools-tab-content">
            {error && <p className="error-message">{error}</p>}

            <div className="tool-section">
                <h4>Split Tool</h4>
                <p>Define multiple regions to create new child files from this image.</p>
                <button
                    onClick={handleSplitClick}
                    disabled={loading}
                    className="tool-button"
                >
                    {splitMode ? 'Editing Regions...' : 'Start Split'}
                </button>

                {splitMode && (
                    <div className="split-editor">
                        <p>Split tool editor placeholder - Coming soon</p>
                        <div className="split-actions">
                            <button onClick={handleSplitSave} disabled={loading}>
                                {loading ? 'Saving...' : 'Save Split'}
                            </button>
                            <button onClick={() => setSplitMode(false)}>Cancel</button>
                        </div>
                    </div>
                )}
            </div>

            <div className="tool-section">
                <h4>Remove Watermark</h4>
                <button
                    disabled
                    className="tool-button disabled"
                    title="Coming soon"
                >
                    Remove Watermark (Coming Soon)
                </button>
            </div>
        </div>
    );
};

// Registration Number Allocation Tool
const RegNumberAllocationTool = ({ asset, plaintiffId, onUpdate }) => {
    const [registrations, setRegistrations] = useState([]);
    const [selectedRegNo, setSelectedRegNo] = useState('');
    const [searchTerm, setSearchTerm] = useState('');
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchRegistrations = async () => {
            if (!plaintiffId) return;
            try {
                const result = await getPlaintiffRegistrations(plaintiffId);
                setRegistrations(result.data || []);
            } catch (err) {
                console.error("Failed to fetch registrations", err);
            }
        };
        fetchRegistrations();
    }, [plaintiffId]);

    const filteredRegistrations = registrations.filter(reg =>
        reg.reg_no.toLowerCase().includes(searchTerm.toLowerCase()) ||
        reg.title.toLowerCase().includes(searchTerm.toLowerCase())
    );

    const handleAssignRegNo = async () => {
        if (!selectedRegNo) return;

        setLoading(true);
        setError(null);
        try {
            await updateCopyrightAsset(asset.id, { registration_number: selectedRegNo });
            onUpdate();
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="reg-allocation-tool">
            <h4>Assign Registration Number</h4>
            {error && <p className="error-message">{error}</p>}

            <div className="current-assignment">
                <label>Current Assignment:</label>
                <span>{asset.reg_no || 'None'}</span>
            </div>

            <div className="search-select">
                <input
                    type="text"
                    placeholder="Search registrations..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="search-input"
                />

                <select
                    value={selectedRegNo}
                    onChange={(e) => setSelectedRegNo(e.target.value)}
                    className="reg-select"
                    size="5"
                >
                    <option value="">Select a registration number</option>
                    {filteredRegistrations.map((reg) => (
                        <option key={reg.reg_no} value={reg.reg_no}>
                            {reg.reg_no} - {reg.title}
                        </option>
                    ))}
                </select>
            </div>
            {/* Type selection will be implemented once the backend supports it for single assets */}
        </div>
    );
};

const CropTab = ({ asset, onUpdate }) => {
    const [crop, setCrop] = useState();
    const [completedCrop, setCompletedCrop] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const imgRef = useRef(null);

    const onImageLoad = (e) => {
        const { width, height } = e.currentTarget;
        const initialCrop = centerCrop(
            makeAspectCrop(
                {
                    unit: '%',
                    width: 90,
                },
                1, // aspect ratio
                width,
                height
            ),
            width,
            height
        );
        setCrop(initialCrop);
    };

    // Utility to get cropped image blob
    function getCroppedImg(image, crop, fileName) {
        const canvas = document.createElement('canvas');
        const scaleX = image.naturalWidth / image.width;
        const scaleY = image.naturalHeight / image.height;
        canvas.width = crop.width;
        canvas.height = crop.height;
        const ctx = canvas.getContext('2d');

        ctx.drawImage(
            image,
            crop.x * scaleX,
            crop.y * scaleY,
            crop.width * scaleX,
            crop.height * scaleY,
            0,
            0,
            crop.width,
            crop.height
        );

        return new Promise((resolve, reject) => {
            canvas.toBlob(blob => {
                if (!blob) {
                    reject(new Error('Canvas is empty'));
                    return;
                }
                blob.name = fileName;
                resolve(blob);
            }, 'image/png');
        });
    }

    const handleSaveCrop = async () => {
        if (!completedCrop || !imgRef.current) {
            setError("Please select a crop area first.");
            return;
        }

        setLoading(true);
        setError(null);

        try {
            // Send crop box to server in original image pixel coordinates
            const scaleX = imgRef.current.naturalWidth / imgRef.current.width;
            const scaleY = imgRef.current.naturalHeight / imgRef.current.height;
            const cropBox = {
                x: Math.max(0, completedCrop.x * scaleX),
                y: Math.max(0, completedCrop.y * scaleY),
                width: Math.max(1, completedCrop.width * scaleX),
                height: Math.max(1, completedCrop.height * scaleY),
            };
            await cropCopyrightAsset(asset.id, cropBox);
            onUpdate(); // Refresh gallery
            alert('Crop saved and original image updated.');
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    if (!asset.high_res_path) {
        return <p>No high-resolution image available to crop.</p>;
    }

    const proxiedImageUrl = asset.high_res_path ? `/api/v1/copyright/image-proxy?url=${encodeURIComponent(asset.high_res_path)}` : '';

    return (
        <div className="crop-tab-content">
            <p>Select an area to crop. Saving will overwrite the original image for this asset.</p>
            <ReactCrop
                crop={crop}
                onChange={(c) => setCrop(c)}
                onComplete={(c) => setCompletedCrop(c)}
                aspect={1}
                className="react-crop-wrapper"
            >
                {/* Use proxied URL to avoid CORS issues with canvas */}
                <img
                    ref={imgRef}
                    src={proxiedImageUrl}
                    alt="Crop preview"
                    onLoad={onImageLoad}
                    style={{ maxWidth: '100%', maxHeight: '400px', display: 'block' }}
                />
            </ReactCrop>
            <button onClick={handleSaveCrop} disabled={loading || !completedCrop} className="button-primary">
                {loading ? 'Saving...' : 'Save Crop'}
            </button>
            {error && <p className="error-message">{error}</p>}
        </div>
    );
};


const ImageDrawer = ({ asset, onClose, onUpdate, plaintiffId }) => {
    const [activeTab, setActiveTab] = useState('info');
    const [isPromoteModalOpen, setPromoteModalOpen] = useState(false);

    if (!asset) return null;

    // Determine if this is a copyrights_files or cn_websites_files asset
    const iscopyrightFile = asset.hasOwnProperty('production');
    const isCnWebsiteFile = asset.hasOwnProperty('docket_in_title');

    const handleInfoTabUpdate = (action) => {
        if (action === 'promote') {
            setPromoteModalOpen(true);
        } else {
            onUpdate();
        }
    };

    const handleTabClick = (tabName) => {
        setActiveTab(tabName);
    };

    const renderTabContent = () => {
        switch (activeTab) {
            case 'info':
                if (iscopyrightFile) {
                    return <CopyrightsFilesInfoTab asset={asset} onUpdate={handleInfoTabUpdate} />;
                } else if (isCnWebsiteFile) {
                    return <CnWebsitesFilesInfoTab asset={asset} onUpdate={handleInfoTabUpdate} />;
                }
                return <div>Unknown asset type</div>;

            case 'tools':
                if (iscopyrightFile) {
                    return <CopyrightsFilesToolsTab asset={asset} onUpdate={onUpdate} />;
                }
                return <div>Tools not available for this asset type</div>;

            case 'crop':
                if (iscopyrightFile) {
                    return <CropTab asset={asset} onUpdate={onUpdate} />;
                }
                return <div>Crop not available for this asset type</div>;

            case 'preview':
                return (
                    <div className="preview-tab-content">
                        {/* High-quality image preview */}
                        <div className="image-preview">
                            {asset.high_res_path ? (
                                <img src={asset.high_res_path} alt={`High-res preview of ${asset.reg_no || asset.registration_number}`} style={{ maxWidth: '100%' }} />
                            ) : (
                                <p>No high-resolution image available.</p>
                            )}
                        </div>

                        {/* Certificate preview (only for copyrights_files) */}
                        {iscopyrightFile && (
                            <div className="certificate-preview">
                                <h4>Certificate</h4>
                                {asset.certificate_path ? (
                                    <img src={asset.certificate_path} alt={`Certificate for ${asset.reg_no}`} style={{ maxWidth: '100%' }} />
                                ) : (
                                    <div className="empty-state">
                                        <p>No certificate available.</p>
                                        <button className="attach-certificate-btn">Attach Certificate</button>
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                );

            case 'reg-allocation':
                if (iscopyrightFile) {
                    return <RegNumberAllocationTool asset={asset} plaintiffId={plaintiffId} onUpdate={onUpdate} />;
                }
                return <div>Registration allocation not available for this asset type</div>;

            default:
                return null;
        }
    };

    const getTabsForAssetType = () => {
        if (iscopyrightFile) {
            return [
                { key: 'info', label: 'Info' },
                { key: 'tools', label: 'Tools' },
                { key: 'preview', label: 'Preview' },
                { key: 'reg-allocation', label: 'Assign RegNo' }
            ];
        } else if (isCnWebsiteFile) {
            return [
                { key: 'info', label: 'Info' }
            ];
        }
        return [{ key: 'info', label: 'Info' }];
    };

    const tabs = getTabsForAssetType();

    return (
        <div className="drawer-overlay" onClick={onClose}>
            <div className="drawer-panel" onClick={(e) => e.stopPropagation()}>
                <div className="drawer-header">
                    <h3>{asset.reg_no || asset.registration_number || 'Unknown'}</h3>
                    <button onClick={onClose} className="drawer-close-btn">&times;</button>
                </div>
                <div className="drawer-tabs">
                    {tabs.map(tab => (
                        <div
                            key={tab.key}
                            className={`drawer-tab ${activeTab === tab.key ? 'active' : ''}`}
                            onClick={() => handleTabClick(tab.key)}
                        >
                            {tab.label}
                        </div>
                    ))}
                </div>
                <div className="drawer-content">
                    {renderTabContent()}
                </div>
                {isPromoteModalOpen && (
                    <PromoteModal
                        asset={asset}
                        onClose={() => setPromoteModalOpen(false)}
                        onUpdate={() => {
                            setPromoteModalOpen(false);
                            onUpdate();
                        }}
                    />
                )}
            </div>
        </div>
    );
};

export default ImageDrawer;