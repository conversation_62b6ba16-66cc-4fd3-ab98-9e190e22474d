import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import ImageDrawer from './ImageDrawer';
import * as api_copyright_viz from '../../services/api_copyright_viz';

// Mock the services
jest.mock('../../services/api_copyright_viz');

// Mock ReactCrop
jest.mock('react-image-crop', () => ({
    __esModule: true,
    default: ({ children }) => <div data-testid="mock-react-crop">{children}</div>,
    centerCrop: jest.fn(),
    makeAspectCrop: jest.fn(),
}));


const mockAsset = {
    viz_id: 1,
    copyright_id: 101,
    registration_number: 'VA 123',
    plaintiff_name: 'Test Plaintiff',
    production: true,
    method: 'exhibit',
    high_res_path: 'high/1.jpg',
    certificate_status: 'fetched'
};

const mockMdAsset = {
    ...mockAsset,
    viz_id: 2,
    registration_number: 'MD-ABC123',
};

describe('ImageDrawer Component', () => {
    const onUpdate = jest.fn();
    const onClose = jest.fn();

    beforeEach(() => {
        api_copyright_viz.updateCopyrightAsset.mockResolvedValue({ success: true });
        api_copyright_viz.cropCopyrightAsset.mockResolvedValue({ success: true });
        api_copyright_viz.promoteCopyrightAsset.mockResolvedValue({ success: true });
        api_copyright_viz.getCopyrightTypes.mockResolvedValue({ data: [] });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('renders nothing when asset is null', () => {
        const { container } = render(<ImageDrawer asset={null} onClose={onClose} onUpdate={onUpdate} />);
        expect(container.firstChild).toBeNull();
    });

    it('renders the drawer with asset info when an asset is provided', () => {
        render(<ImageDrawer asset={mockAsset} onClose={onClose} onUpdate={onUpdate} />);
        expect(screen.getByRole('heading', { name: 'VA 123' })).toBeInTheDocument();
        expect(screen.getByDisplayValue('exhibit')).toBeInTheDocument();
    });

    it('allows switching between tabs', () => {
        render(<ImageDrawer asset={mockAsset} onClose={onClose} onUpdate={onUpdate} />);
        
        // Starts on Info tab
        expect(screen.getByLabelText('Production')).toBeInTheDocument();

        // Switch to Crop tab
        fireEvent.click(screen.getByText('Crop'));
        expect(screen.getByTestId('mock-react-crop')).toBeInTheDocument();

        // Switch to Preview tab
        fireEvent.click(screen.getByText('Preview'));
        expect(screen.getByAltText(/High-res preview/i)).toBeInTheDocument();
    });

    it('calls update service when an info field is changed', async () => {
        render(<ImageDrawer asset={mockAsset} onClose={onClose} onUpdate={onUpdate} />);
        
        const productionCheckbox = screen.getByLabelText('Production');
        fireEvent.click(productionCheckbox);

        await waitFor(() => {
            expect(api_copyright_viz.updateCopyrightAsset).toHaveBeenCalledWith(1, { production: false });
            expect(onUpdate).toHaveBeenCalled();
        });
    });

    it('shows promote button for MD assets and opens modal on click', async () => {
        render(<ImageDrawer asset={mockMdAsset} onClose={onClose} onUpdate={onUpdate} />);
        
        const promoteButton = screen.getByRole('button', { name: /Promote/i });
        expect(promoteButton).toBeInTheDocument();

        fireEvent.click(promoteButton);

        await waitFor(() => {
            expect(screen.getByRole('heading', { name: /Promote Asset/i })).toBeInTheDocument();
        });
    });

    it('calls promote service when promote form is submitted', async () => {
        render(<ImageDrawer asset={mockMdAsset} onClose={onClose} onUpdate={onUpdate} />);
        
        fireEvent.click(screen.getByRole('button', { name: /Promote/i }));

        await waitFor(() => screen.getByLabelText(/New Registration Number/i));

        const input = screen.getByLabelText(/New Registration Number/i);
        fireEvent.change(input, { target: { value: 'VA 789' } });

        const submitButton = screen.getByRole('button', { name: 'Promote' });
        fireEvent.click(submitButton);

        await waitFor(() => {
            expect(api_copyright_viz.promoteCopyrightAsset).toHaveBeenCalledWith(2, 'VA 789');
            expect(onUpdate).toHaveBeenCalled();
        });
    });
});