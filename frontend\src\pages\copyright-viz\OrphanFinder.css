.orphan-finder-container {
    padding: 2rem;
    font-family: sans-serif;
    background-color: #f4f6f8;
}

.orphan-header {
    text-align: center;
    margin-bottom: 2rem;
}

.orphan-tabs {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
    border-bottom: 1px solid #ddd;
}

.tab-button {
    padding: 1rem 1.5rem;
    border: none;
    background: none;
    cursor: pointer;
    font-size: 1rem;
    color: #555;
    border-bottom: 3px solid transparent;
    margin-bottom: -1px;
}

.tab-button.active {
    color: #007bff;
    border-bottom-color: #007bff;
}

.orphan-content {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.orphan-panel {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.orphan-actions {
    display: flex;
    justify-content: flex-end;
}

.orphan-table {
    width: 100%;
    border-collapse: collapse;
}

.orphan-table th, .orphan-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.orphan-table th {
    background-color: #f8f9fa;
}

.no-orphans {
    text-align: center;
    color: #888;
    padding: 2rem;
}

.error-message {
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    padding: 1rem;
    border-radius: 4px;
    text-align: center;
}

.loading-spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 4rem auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}