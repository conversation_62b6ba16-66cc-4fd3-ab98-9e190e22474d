import React, { useState, useEffect, useCallback } from 'react';
import { getOrphans, fixOrphans } from '../../services/api_copyright_viz';
import './OrphanFinder.css';
const ORPHAN_CATEGORIES = [
    { id: 'court-only', name: 'Court-Only' },
    { id: 'db-only', name: 'DB-Only' },
    { id: 'prod-no-qdrant', name: 'Prod w/o Qdrant' },
    { id: 'qdrant-only', name: 'Qdrant-Only' },
    { id: 'missing-cos', name: 'Missing COS' },
    { id: 'duplicate-reg-no', name: 'Duplicate Reg-No' },
];

const OrphanCategoryPanel = ({ category, onFix, onRefresh }) => {
    const [items, setItems] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [selected, setSelected] = useState(new Set());

    const fetchData = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const result = await getOrphans(category.id);
            setItems(result.data || []);
        } catch (err) {
            if (err.message.includes('Not implemented')) {
                setError(`This orphan category (${category.name}) is not yet implemented in the backend.`);
            } else {
                setError(err.message);
            }
            setItems([]);
        } finally {
            setLoading(false);
        }
    }, [category]);

    useEffect(() => {
        fetchData();
    }, [fetchData]);

    const handleSelectAll = (e) => {
        if (e.target.checked) {
            setSelected(new Set(items.map(item => item.id)));
        } else {
            setSelected(new Set());
        }
    };

    const handleSelectOne = (id) => {
        const newSelected = new Set(selected);
        if (newSelected.has(id)) {
            newSelected.delete(id);
        } else {
            newSelected.add(id);
        }
        setSelected(newSelected);
    };

    const handleFix = async () => {
        await onFix(category.id, 'delete', Array.from(selected));
        setSelected(new Set());
        onRefresh(); // This will trigger a re-fetch in the parent
    };

    if (loading) return <div className="loading-spinner"></div>;

    return (
        <div className="orphan-panel">
            {error && <p className="error-message">{error}</p>}
            <div className="orphan-actions">
                <button onClick={handleFix} disabled={selected.size === 0}>
                    Delete Selected ({selected.size})
                </button>
            </div>
            <table className="orphan-table">
                <thead>
                    <tr>
                        <th>
                            <input
                                type="checkbox"
                                onChange={handleSelectAll}
                                checked={selected.size > 0 && selected.size === items.length}
                            />
                        </th>
                        <th>ID / Path</th>
                        <th>Details</th>
                    </tr>
                </thead>
                <tbody>
                    {items.length > 0 ? items.map(item => (
                        <tr key={item.id}>
                            <td>
                                <input
                                    type="checkbox"
                                    checked={selected.has(item.id)}
                                    onChange={() => handleSelectOne(item.id)}
                                />
                            </td>
                            <td>{item.id}</td>
                            <td>{item.details || JSON.stringify(item)}</td>
                        </tr>
                    )) : (
                        <tr>
                            <td colSpan="3" className="no-orphans">No orphans found in this category.</td>
                        </tr>
                    )}
                </tbody>
            </table>
        </div>
    );
};

const OrphanFinder = () => {
    const [activeTab, setActiveTab] = useState(ORPHAN_CATEGORIES[0]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);
    const [refreshKey, setRefreshKey] = useState(0);

    const handleFixOrphans = async (category, action, ids) => {
        if (ids.length === 0) return;
        setLoading(true);
        setError(null);
        try {
            await fixOrphans({ category, action, ids });
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="orphan-finder-container">
            <div className="orphan-header">
                <h1>Orphan Finder</h1>
                <p>Find and resolve orphaned assets across the system.</p>
            </div>
            {error && <p className="error-message">{error}</p>}
            {loading && <p>Processing fix...</p>}
            <div className="orphan-tabs">
                {ORPHAN_CATEGORIES.map(cat => (
                    <button
                        key={cat.id}
                        className={`tab-button ${activeTab.id === cat.id ? 'active' : ''}`}
                        onClick={() => setActiveTab(cat)}
                    >
                        {cat.name}
                    </button>
                ))}
            </div>
            <div className="orphan-content">
                <OrphanCategoryPanel
                    key={`${activeTab.id}-${refreshKey}`}
                    category={activeTab}
                    onFix={handleFixOrphans}
                    onRefresh={() => setRefreshKey(k => k + 1)}
                />
            </div>
        </div>
    );
};

export default OrphanFinder;