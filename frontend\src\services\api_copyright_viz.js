const API_URL = '/api/v1/copyright';

const handleResponse = async (response) => {
  if (!response.ok) {
    // Try to parse standardized backend error structure
    let msg = 'Something went wrong';
    try {
      const error = await response.json();
      msg = error?.error || error?.message || msg;
    } catch (_) {
      // ignore parse errors, fall back to status text
      msg = response.statusText || msg;
    }
    throw new Error(msg);
  }
  return response.json();
};

export const getCopyrightAssets = async (filters) => {
  // Normalize filters: trim plaintiff_name and drop empty values
  const normalized = {};
  Object.entries(filters || {}).forEach(([k, v]) => {
    if (v === null || v === undefined) return;
    if (typeof v === 'string') {
      const t = v.trim();
      if (t !== '') normalized[k] = t;
    } else {
      normalized[k] = v;
    }
  });

  const query = new URLSearchParams(normalized).toString();
  const response = await fetch(`${API_URL}?${query}`);
  return handleResponse(response);
};

export const bulkUpdateCopyrightAssets = async (updates) => {
  const response = await fetch(`${API_URL}/bulk`, {
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(updates),
  });
  return handleResponse(response);
};

export const addCopyrightAsset = async (assetData) => {
  const response = await fetch(API_URL, {
    method: 'POST',
    body: assetData, // Assuming FormData
  });
  return handleResponse(response);
};

export const updateCopyrightAsset = async (id, assetData) => {
  // NOTE: The API design document does not specify an endpoint for updating a single asset's details.
  // This implementation assumes a PATCH /api/v1/copyright/{id} endpoint exists for this purpose.
  const response = await fetch(`${API_URL}/${id}`, {
    method: 'PATCH',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(assetData),
  });
  return handleResponse(response);
};

export const cropCopyrightAsset = async (id, cropBox) => {
  // Server-side crop now expects JSON { x, y, width, height }
  const response = await fetch(`${API_URL}/${id}/crop`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(cropBox),
  });
  return handleResponse(response);
};

export const promoteCopyrightAsset = async (id, registrationNumber) => {
  const response = await fetch(`${API_URL}/${id}/promote`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ registration_number: registrationNumber }),
  });
  return handleResponse(response);
};

export const getNextDuplicatePair = async () => {
  const response = await fetch(`${API_URL}/duplicates/next`);
  return handleResponse(response);
};

export const resolveDuplicatePair = async (resolutionData) => {
  const response = await fetch(`${API_URL}/duplicates/resolve`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(resolutionData),
  });
  return handleResponse(response);
};

export const getOrphans = async (category) => {
  const response = await fetch(`${API_URL}/orphans/${category}`);
  return handleResponse(response);
};

export const fixOrphans = async (fixData) => {
  const response = await fetch(`${API_URL}/orphans/fix`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(fixData),
  });
  return handleResponse(response);
};

export const refreshCache = async () => {
  const response = await fetch(`${API_URL}/cache/refresh`, {
    method: 'POST',
  });
  return handleResponse(response);
};

export const getCopyrightTypes = async () => {
  const response = await fetch(`${API_URL}/types`);
  return handleResponse(response);
};

export const getCopyrightMethods = async () => {
  const response = await fetch(`${API_URL}/methods`);
  return handleResponse(response);
};

export const manageCopyrightTypes = async (typeData) => {
  const response = await fetch(`${API_URL}/types`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(typeData),
  });
  return handleResponse(response);
};

// --- New API calls for Copyright Gallery v2 ---

export const moveCnWebsitesFiles = async (ids) => {
  const response = await fetch(`${API_URL}/cn-websites-files/move`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ ids }),
  });
  return handleResponse(response);
};

export const bulkUpdateCopyrightsFiles = async (op, ids, value) => {
  const response = await fetch(`${API_URL}/copyrights-files/bulk`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ op, ids, value }),
  });
  return handleResponse(response);
};

export const getPlaintiffRegistrations = async (plaintiffId) => {
  const response = await fetch(`${API_URL}/plaintiffs/${plaintiffId}/registrations`);
  return handleResponse(response);
};

export const splitCopyrightFile = async (fileId, regions, inherit) => {
  const response = await fetch(`${API_URL}/copyrights-files/${fileId}/split`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ regions, inherit }),
  });
  return handleResponse(response);
};